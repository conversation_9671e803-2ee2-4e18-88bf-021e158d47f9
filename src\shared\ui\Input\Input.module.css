/* Input Group */
.inputGroup {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--space-md);
}

.fullWidth {
  width: 100%;
}

/* Label */
.labelWrapper {
  margin-bottom: var(--space-xs);
}

.label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  display: inline-block;
}

.required {
  color: var(--color-error);
  margin-left: var(--space-xs);
}

/* Input Wrapper */
.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  transition: all var(--transition-normal);
}

/* Variants */
.variant-default {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.variant-default:focus-within {
  border-color: var(--border-active);
  box-shadow: var(--shadow-md);
}

.variant-filled {
  background-color: rgba(255, 255, 255, 0.04);
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
}

.variant-filled:focus-within {
  background-color: rgba(255, 255, 255, 0.06);
  box-shadow: var(--shadow-sm);
}

.variant-flushed {
  background-color: transparent;
  border: none;
  border-bottom: 1px solid var(--border-color);
  border-radius: 0;
}

.variant-flushed:focus-within {
  border-bottom-color: var(--border-active);
}

.variant-unstyled {
  background-color: transparent;
  border: none;
  border-radius: 0;
}

/* Sizes */
.size-sm {
  height: 30px;
}

.size-md {
  height: 36px;
}

.size-lg {
  height: 44px;
}

/* Icon positioning */
.leftIcon,
.rightIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  position: absolute;
  pointer-events: none;
}

.leftIcon {
  left: var(--space-sm);
}

.rightIcon {
  right: var(--space-sm);
}

.hasLeftIcon .input {
  padding-left: calc(var(--space-sm) * 2 + 16px); /* icon width + padding */
}

.hasRightIcon .input {
  padding-right: calc(var(--space-sm) * 2 + 16px); /* icon width + padding */
}

/* Input */
.input {
  width: 100%;
  height: 100%;
  padding: 0 var(--space-sm);
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-family: var(--font-main);
  outline: none;
}

.input::placeholder {
  color: var(--text-muted);
}

/* Error State */
.hasError .variant-default {
  border-color: var(--color-error);
}

.hasError .variant-filled {
  border-color: var(--color-error);
}

.hasError .variant-flushed {
  border-bottom-color: var(--color-error);
}

.errorMessage {
  color: var(--color-error);
  font-size: var(--font-size-xs);
  margin-top: var(--space-xs);
}

/* Hint */
.hint {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  margin-top: var(--space-xs);
}

/* Disabled State */
.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.disabled .input {
  cursor: not-allowed;
}

/* Cyberpunk-specific styling */
.variant-default:focus-within,
.variant-filled:focus-within {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 0 1px var(--neon-cyan-muted);
}

/* Typing animation for focused inputs */
.inputWrapper:focus-within::after {
  content: "_";
  position: absolute;
  right: var(--space-sm);
  color: var(--neon-cyan);
  animation: blink 1s step-end infinite;
}

.hasRightIcon:focus-within::after {
  display: none;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}
