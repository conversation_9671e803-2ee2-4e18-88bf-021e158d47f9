# Team Management System Architecture

## Overview

The Team Management system in CyberClash allows players to recruit, manage, and assign specialists with unique skills and attributes. Like other game systems, it follows the Core Layer architecture pattern to separate game logic from UI and state management.

## Core Components

### Data Models

The specialist system is built around these key types (defined in `src/core/specialist/types.ts`):

- **Specialist**: Team member with skills, attributes, and specializations
- **SkillType**: Different skill categories specialists can possess (hacking, social_engineering, etc.)
- **TraitType**: Character traits affecting specialist behavior
- **SpecializationType**: Specific role or expertise (malware_developer, social_engineer, etc.)
- **SpecialistStatus**: Current status of a specialist (available, hired, on_mission, training)

### Specialist Service

The SpecialistService (`src/core/specialist/SpecialistService.ts`) handles the game logic including:

- Hiring and firing specialists
- Assigning specialists to missions
- Managing specialist training
- Creating and managing teams
- Calculating experience and level progression

It provides a clean API through the interfaces defined in `src/core/specialist/interfaces.ts`.

### Repository

The specialist system uses three main repository interfaces:

- **SpecialistRepository**: Managing individual specialists
- **TeamRepository**: Creating and managing teams
- **TrainingRepository**: Managing training programs

Implemented using Zustand for state management via:

- ZustandSpecialistRepository
- ZustandTeamRepository (to be implemented)
- ZustandTrainingRepository (to be implemented)

## Feature Implementation

### State Management

Specialist state is managed through Zustand with:

- Specialist store (`src/features/specialist/model/store.ts`)
- Async actions for specialist operations
- Selector hooks for data access

Key state includes:
- Available specialists for hire
- Hired specialists
- Teams configuration
- Training programs

### UI Components

The team management UI consists of:

1. **TeamPage**: Main interface for team management
2. **SpecialistCard**: Display card showing specialist details
3. **TrainingView**: Interface for training specialists (planned)
4. **TeamView**: Interface for team formation (planned)

## Team Management Flow

### 1. Recruitment

Players acquire specialists through:
- Browsing available specialists
- Hiring based on needed skills
- Managing recruitment budget
- Evaluating specialist attributes

### 2. Development

Specialists can be improved through:
- Training programs (planned)
- Mission experience
- Skill specialization

### 3. Assignment

Specialists can be assigned to:
- Active missions
- Training programs

### 4. Management

Ongoing team management includes:
- Evaluating specialist performance
- Firing underperforming specialists
- Team composition optimization

## Integration Points

### Mission Integration

Specialists are crucial for mission success:

- Required specializations for objectives
- Skill checks during mission execution
- Experience gain from completed missions

### Marketplace Integration

Specialists interact with the market through:

- Recruitment costs
- Training program expenses

## Implementation Status

The specialist system has these components implemented:

- ✅ Core data models and types
- ✅ Specialist service with core functionality
- ✅ Repository interfaces and Redux implementation
- ✅ Basic UI for hiring and viewing specialists
- ⏳ Training program implementation
- ⏳ Team formation functionality
- ⏳ Specialist detail view

## Next Steps

To continue implementation:

1. Complete the training program interface and functionality
2. Implement team formation system
3. Create detailed specialist profile view
4. Connect with mission system for specialist assignment
