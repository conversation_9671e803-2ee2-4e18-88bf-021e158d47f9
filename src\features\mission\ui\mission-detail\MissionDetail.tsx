import React from 'react';
import { Mission, Objective, MissionProgress } from '@/core/mission/types';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@/shared/ui';
import {
  IconArrowLeft,
  IconTargetArrow,
  IconAlertTriangle,
  IconClock,
  IconTrophy,
  IconLocation,
  IconServer,
  IconTools,
  IconCheck,
  IconX,
  IconPlayerPlay,
  IconCoin,
  IconStar,
  IconArrowUp,
  IconArrowDown,
  IconListCheck
} from '@tabler/icons-react';
import styles from './MissionDetail.module.css';
import clsx from 'clsx';

interface MissionDetailProps {
  mission: Mission;
  progress?: MissionProgress | null;
  onStartMission?: () => void;
  onCompleteObjective?: (objectiveId: string) => void;
  onBack?: () => void;
  className?: string;
}

export const MissionDetail: React.FC<MissionDetailProps> = ({
  mission,
  progress,
  onStartMission,
  onCompleteObjective,
  onBack,
  className
}) => {
  // Get difficulty details
  const getDifficultyDetails = (difficulty: string): {
    color: 'success' | 'info' | 'warning' | 'danger' | 'secondary';
    label: string;
  } => {
    switch (difficulty) {
      case 'novice':
        return { color: 'success', label: 'Novice' };
      case 'professional':
        return { color: 'info', label: 'Professional' };
      case 'expert':
        return { color: 'warning', label: 'Expert' };
      case 'elite':
        return { color: 'danger', label: 'Elite' };
      case 'legendary':
        return { color: 'secondary', label: 'Legendary' };
      default:
        return { color: 'info', label: difficulty };
    }
  };
  
  // Format risk level to show as stars
  const formatRiskLevel = (level: number) => {
    return (
      <div className={styles.riskStars}>
        {Array.from({ length: 10 }).map((_, index) => (
          <span 
            key={index} 
            className={clsx(
              styles.riskStar, 
              index < level ? styles.filledStar : styles.emptyStar
            )}
          >
            ★
          </span>
        ))}
        <span className={styles.riskValue}>{level}/10</span>
      </div>
    );
  };
  
  // Get mission type icon and label
  const getMissionTypeInfo = (type: string): { icon: React.ReactNode; label: string } => {
    const formattedType = type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    
    switch (type) {
      case 'hack':
        return { 
          icon: <IconTargetArrow size={16} stroke={1.5} />, 
          label: 'System Breach' 
        };
      case 'data_extraction':
        return { 
          icon: <IconServer size={16} stroke={1.5} />, 
          label: 'Data Extraction' 
        };
      case 'intelligence':
        return { 
          icon: <IconListCheck size={16} stroke={1.5} />, 
          label: 'Intelligence' 
        };
      case 'defense':
        return { 
          icon: <IconTools size={16} stroke={1.5} />, 
          label: 'Defense' 
        };
      case 'forensic':
        return { 
          icon: <IconTargetArrow size={16} stroke={1.5} />, 
          label: 'Forensic Analysis' 
        };
      default:
        return { 
          icon: <IconTargetArrow size={16} stroke={1.5} />, 
          label: formattedType 
        };
    }
  };
  
  // Get status for an objective
  const getObjectiveStatus = (objective: Objective): 'completed' | 'in_progress' | 'failed' | 'pending' => {
    if (!progress) return objective.status as any;
    
    if (progress.completedObjectives.includes(objective.id)) {
      return 'completed';
    }
    
    if (progress.failedObjectives.includes(objective.id)) {
      return 'failed';
    }
    
    if (progress.currentObjective === objective.id) {
      return 'in_progress';
    }
    
    return 'pending';
  };
  
  // Get color and icon for objective status
  const getObjectiveStatusDetails = (status: string): {
    color: 'success' | 'info' | 'warning' | 'danger' | 'secondary';
    icon: React.ReactNode;
    label: string;
  } => {
    switch (status) {
      case 'completed':
        return { 
          color: 'success', 
          icon: <IconCheck size={16} stroke={1.5} />,
          label: 'Completed'
        };
      case 'in_progress':
        return { 
          color: 'info', 
          icon: <IconPlayerPlay size={16} stroke={1.5} />,
          label: 'In Progress'
        };
      case 'failed':
        return { 
          color: 'danger', 
          icon: <IconX size={16} stroke={1.5} />,
          label: 'Failed'
        };
      default:
        return { 
          color: 'secondary', 
          icon: <IconClock size={16} stroke={1.5} />,
          label: 'Pending'
        };
    }
  };
  
  // Check if an objective can be started/completed
  const isObjectiveAvailable = (objective: Objective): boolean => {
    if (!progress) return false;
    
    // If already completed or failed, it's not available
    if (progress.completedObjectives.includes(objective.id) || 
        progress.failedObjectives.includes(objective.id)) {
      return false;
    }
    
    // If it has dependencies, check if they are completed
    if (objective.dependsOn && objective.dependsOn.length > 0) {
      return objective.dependsOn.every(depId => 
        progress.completedObjectives.includes(depId)
      );
    }
    
    // If no dependencies, it's available
    return true;
  };
  
  const difficultyInfo = getDifficultyDetails(mission.difficulty);
  const typeInfo = getMissionTypeInfo(mission.type);
  
  return (
    <Card 
      className={clsx(styles.missionDetail, className)} 
      variant="elevated"
      accent={difficultyInfo.color}
    >
      <CardHeader 
        title={
          <div className={styles.headerTitle}>
            {onBack && (
              <Button 
                variant="ghost" 
                size="sm" 
                leftIcon={<IconArrowLeft size={16} stroke={1.5} />}
                onClick={onBack}
                className={styles.backButton}
              >
                Back
              </Button>
            )}
            <span>{mission.title}</span>
          </div>
        }
        subtitle={
          <div className={styles.headerSubtitle}>
            <Badge colorScheme={difficultyInfo.color} variant="subtle">
              {difficultyInfo.label}
            </Badge>
            <Badge colorScheme="secondary" variant="subtle" leftIcon={typeInfo.icon}>
              {typeInfo.label}
            </Badge>
          </div>
        }
      />
      
      <CardBody className={styles.missionContent}>
        <section className={styles.briefSection}>
          <h3 className={styles.sectionTitle}>
            <IconAlertTriangle size={18} className={styles.sectionIcon} stroke={1.5} />
            Mission Brief
          </h3>
          <Card variant="default" className={styles.briefCard}>
            <CardBody>
              <p className={styles.briefText}>{mission.brief}</p>
            </CardBody>
          </Card>
        </section>
        
        <div className={styles.columnsContainer}>
          <Card className={styles.detailsSection} variant="default">
            <CardHeader 
              title={
                <div className={styles.sectionHeading}>
                  <IconListCheck size={18} className={styles.sectionIcon} stroke={1.5} />
                  Mission Details
                </div>
              }
            />
            <CardBody>
              <div className={styles.details}>
                <div className={styles.detailRow}>
                  <span className={styles.label}>Faction:</span>
                  <span className={styles.value}>{mission.faction}</span>
                </div>
                
                <div className={styles.detailRow}>
                  <span className={styles.label}>Risk Level:</span>
                  <span className={styles.value}>
                    {formatRiskLevel(mission.riskLevel)}
                  </span>
                </div>
                
                {mission.location && (
                  <div className={styles.detailRow}>
                    <span className={styles.label}>
                      <IconLocation size={16} className={styles.detailIcon} stroke={1.5} />
                      Location:
                    </span>
                    <span className={styles.value}>{mission.location}</span>
                  </div>
                )}
                
                {mission.targetSystem && (
                  <div className={styles.detailRow}>
                    <span className={styles.label}>
                      <IconServer size={16} className={styles.detailIcon} stroke={1.5} />
                      Target System:
                    </span>
                    <span className={styles.value}>{mission.targetSystem}</span>
                  </div>
                )}
                
                {mission.timeConstraints && (
                  <div className={styles.detailRow}>
                    <span className={styles.label}>
                      <IconClock size={16} className={styles.detailIcon} stroke={1.5} />
                      Time Limit:
                    </span>
                    <span className={styles.value}>
                      {mission.timeConstraints.totalTime} minutes
                    </span>
                  </div>
                )}
                
                {mission.requiredSpecializations.length > 0 && (
                  <div className={styles.detailRow}>
                    <span className={styles.label}>
                      <IconTools size={16} className={styles.detailIcon} stroke={1.5} />
                      Required Skills:
                    </span>
                    <div className={styles.skillsContainer}>
                      {mission.requiredSpecializations.map(skill => (
                        <Badge key={skill} colorScheme="info" variant="outline" size="sm">
                          {skill.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
          
          <Card className={styles.rewardsSection} variant="default">
            <CardHeader 
              title={
                <div className={styles.sectionHeading}>
                  <IconTrophy size={18} className={styles.sectionIcon} stroke={1.5} />
                  Rewards
                </div>
              }
            />
            <CardBody>
              <ul className={styles.rewardsList}>
                {mission.rewards.credits && (
                  <li className={styles.rewardItem}>
                    <div className={styles.rewardIcon}>
                      <IconCoin size={18} stroke={1.5} />
                    </div>
                    <span className={styles.rewardValue}>{mission.rewards.credits}</span>
                    <span className={styles.rewardLabel}>Credits</span>
                  </li>
                )}
                {mission.rewards.experience && (
                  <li className={styles.rewardItem}>
                    <div className={styles.rewardIcon}>
                      <IconStar size={18} stroke={1.5} />
                    </div>
                    <span className={styles.rewardValue}>{mission.rewards.experience}</span>
                    <span className={styles.rewardLabel}>Experience</span>
                  </li>
                )}
                {mission.rewards.reputation && Object.entries(mission.rewards.reputation).map(([faction, amount]) => (
                  <li key={faction} className={styles.rewardItem}>
                    <div className={styles.rewardIcon}>
                      {amount > 0 ? 
                        <IconArrowUp size={18} stroke={1.5} className={styles.positiveIcon} /> : 
                        <IconArrowDown size={18} stroke={1.5} className={styles.negativeIcon} />
                      }
                    </div>
                    <span className={clsx(
                      styles.rewardValue, 
                      amount > 0 ? styles.positiveValue : styles.negativeValue
                    )}>
                      {amount > 0 ? '+' : ''}{amount}
                    </span>
                    <span className={styles.rewardLabel}>{faction} Reputation</span>
                  </li>
                ))}
              </ul>
            </CardBody>
          </Card>
        </div>
        
        <Card className={styles.objectivesSection} variant="default">
          <CardHeader 
            title={
              <div className={styles.sectionHeading}>
                <IconListCheck size={18} className={styles.sectionIcon} stroke={1.5} />
                Objectives
              </div>
            }
          />
          <CardBody>
            <ul className={styles.objectivesList}>
              {mission.objectives.map(objective => {
                const status = getObjectiveStatus(objective);
                const statusDetails = getObjectiveStatusDetails(status);
                const isAvailable = isObjectiveAvailable(objective);
                
                return (
                  <li 
                    key={objective.id} 
                    className={clsx(
                      styles.objectiveItem, 
                      styles[status]
                    )}
                  >
                    <div className={styles.objectiveHeader}>
                      <h4 className={styles.objectiveTitle}>
                        {objective.title}
                        {objective.required && (
                          <Badge 
                            colorScheme="danger" 
                            variant="subtle" 
                            size="sm"
                            className={styles.requiredBadge}
                          >
                            Required
                          </Badge>
                        )}
                      </h4>
                      <Badge 
                        colorScheme={statusDetails.color} 
                        variant="subtle" 
                        size="sm"
                        leftIcon={statusDetails.icon}
                      >
                        {statusDetails.label}
                      </Badge>
                    </div>
                    <p className={styles.objectiveDescription}>{objective.description}</p>
                    
                    {status === 'in_progress' && isAvailable && onCompleteObjective && (
                      <Button 
                        variant="primary" 
                        size="sm"
                        leftIcon={<IconCheck size={16} stroke={1.5} />}
                        onClick={() => onCompleteObjective(objective.id)}
                      >
                        Complete Objective
                      </Button>
                    )}
                  </li>
                );
              })}
            </ul>
          </CardBody>
        </Card>
      </CardBody>
      
      {mission.status === 'available' && onStartMission && (
        <CardFooter align="right">
          <Button 
            variant="primary" 
            size="md"
            leftIcon={<IconPlayerPlay size={18} stroke={1.5} />}
            onClick={onStartMission}
            className={styles.startButton}
          >
            Start Mission
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};
