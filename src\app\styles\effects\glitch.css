/* Glitch effects */

/* Basic glitch */
.glitch {
  animation: glitch 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both infinite;
  animation-play-state: paused;
  position: relative;
}

.glitch:hover {
  animation-play-state: running;
}

@keyframes glitch {
  0% {
    transform: translate(0);
    text-shadow: 0 0 0 rgba(0, 240, 255, 0);
  }
  20% {
    transform: translate(-2px, 2px);
    text-shadow: -2px 0 var(--glitch-cyan);
  }
  40% {
    transform: translate(-2px, -2px);
    text-shadow: 2px 0 var(--glitch-pink);
  }
  60% {
    transform: translate(2px, 2px);
    text-shadow: 0 -2px var(--glitch-yellow);
  }
  80% {
    transform: translate(2px, -2px);
    text-shadow: -2px 0 var(--glitch-cyan);
  }
  100% {
    transform: translate(0);
    text-shadow: 0 0 0 rgba(0, 240, 255, 0);
  }
}

/* Advanced glitch effect with before/after elements */
.glitch-advanced {
  position: relative;
  color: var(--text-primary);
}

.glitch-advanced::before,
.glitch-advanced::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

.glitch-advanced::before {
  animation: glitch-advanced-1 0.5s infinite;
  color: var(--neon-cyan);
  z-index: -1;
}

.glitch-advanced::after {
  animation: glitch-advanced-2 0.5s infinite;
  color: var(--neon-pink);
  z-index: -2;
}

@keyframes glitch-advanced-1 {
  0% { transform: translate(0); }
  10% { transform: translate(-2px, -2px); }
  20% { transform: translate(2px, 2px); }
  30% { transform: translate(-2px, 2px); }
  40% { transform: translate(2px, -2px); }
  50% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  70% { transform: translate(-2px, 2px); }
  80% { transform: translate(2px, -2px); }
  90% { transform: translate(-2px, -2px); }
  100% { transform: translate(0); }
}

@keyframes glitch-advanced-2 {
  0% { transform: translate(0); }
  10% { transform: translate(2px, 2px); }
  20% { transform: translate(-2px, -2px); }
  30% { transform: translate(2px, -2px); }
  40% { transform: translate(-2px, 2px); }
  50% { transform: translate(2px, 2px); }
  60% { transform: translate(-2px, -2px); }
  70% { transform: translate(2px, -2px); }
  80% { transform: translate(-2px, 2px); }
  90% { transform: translate(2px, 2px); }
  100% { transform: translate(0); }
}

/* Text glitch */
.text-glitch {
  position: relative;
  display: inline-block;
  color: var(--text-primary);
}

.text-glitch::before,
.text-glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  clip: rect(0, 0, 0, 0);
}

.text-glitch::before {
  left: -2px;
  text-shadow: 1px 0 var(--neon-cyan);
  animation: text-glitch-1 0.8s infinite linear alternate-reverse;
}

.text-glitch::after {
  left: 2px;
  text-shadow: -1px 0 var(--neon-pink);
  animation: text-glitch-2 1.2s infinite linear alternate-reverse;
}

@keyframes text-glitch-1 {
  0% {
    clip: rect(44px, 9999px, 56px, 0);
  }
  5% {
    clip: rect(74px, 9999px, 25px, 0);
  }
  10% {
    clip: rect(89px, 9999px, 98px, 0);
  }
  /* More keyframes... */
  100% {
    clip: rect(42px, 9999px, 73px, 0);
  }
}

@keyframes text-glitch-2 {
  0% {
    clip: rect(12px, 9999px, 71px, 0);
  }
  5% {
    clip: rect(22px, 9999px, 5px, 0);
  }
  10% {
    clip: rect(52px, 9999px, 27px, 0);
  }
  /* More keyframes... */
  100% {
    clip: rect(6px, 9999px, 98px, 0);
  }
}
