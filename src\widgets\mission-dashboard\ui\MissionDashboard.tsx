import React from 'react';
import { MissionCard } from '@/features/mission';
import { MissionList } from '@/features/mission';
import { MissionDetail } from '@/features/mission';

interface MissionDashboardProps {
  className?: string;
  showDetails?: boolean;
}

const MissionDashboard: React.FC<MissionDashboardProps> = ({ 
  className,
  showDetails = false
}) => {
  const [selectedMissionId, setSelectedMissionId] = React.useState<string | null>(null);

  return (
    <div className={`mission-dashboard ${className || ''}`}>
      <div className="mission-dashboard__header">
        <h1>Mission Dashboard</h1>
      </div>
      
      <div className="mission-dashboard__content">
        <div className="mission-dashboard__list">
          <MissionList 
            onSelectMission={(missionId) => setSelectedMissionId(missionId)}
            selectedMissionId={selectedMissionId}
          />
        </div>
        
        {showDetails && selectedMissionId && (
          <div className="mission-dashboard__detail">
            <MissionDetail missionId={selectedMissionId} />
          </div>
        )}
      </div>
    </div>
  );
};

export default MissionDashboard;