import React from 'react';
import { Specialist, SpecialistStatus, Trait } from '@/core/specialist/types';
import { Card, CardHeader, CardBody, Badge, Button } from '@/shared/ui';
import styles from './SpecialistDetail.module.css';
import {
  IconArrowLeft,
  IconUser,
  IconUserPlus,
  IconBriefcase,
  IconSchool,
  IconUserMinus,
  IconChartBar,
  IconId,
  IconClipboardList,
  IconHistory,
  IconAward,
  IconThumbUp,
  IconThumbDown,
  IconCalendarEvent
} from '@tabler/icons-react';
import clsx from 'clsx';

interface SpecialistDetailProps {
  specialist: Specialist;
  onBack?: () => void;
  onHire?: (specialistId: string) => void;
  onFire?: (specialistId: string) => void;
  onTrain?: (specialistId: string) => void;
  onAssign?: (specialistId: string) => void;
  className?: string;
}

const SpecialistDetail: React.FC<SpecialistDetailProps> = ({
  specialist,
  onBack,
  onHire,
  onFire,
  onTrain,
  onAssign,
  className
}) => {
  // Format a specialization or skill string for display
  const formatString = (str: string): string => {
    return str.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };
  
  // Generate initials from name
  const getInitials = (name: string): string => {
    return name.split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .join('');
  };
  
  // Get status details
  const getStatusDetails = (status: SpecialistStatus): {
    label: string;
    color: 'success' | 'info' | 'warning' | 'danger' | 'secondary';
  } => {
    switch (status) {
      case 'available':
        return { label: 'Available', color: 'success' };
      case 'hired':
        return { label: 'Hired', color: 'info' };
      case 'on_mission':
        return { label: 'On Mission', color: 'danger' };
      case 'training':
        return { label: 'Training', color: 'warning' };
      case 'unavailable':
        return { label: 'Unavailable', color: 'secondary' };
      default:
        return { label: status, color: 'secondary' };
    }
  };
  
  // Get top skills (sorted by value)
  const topSkills = Object.entries(specialist.skills)
    .sort(([, valueA], [, valueB]) => valueB - valueA);
  
  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const statusInfo = getStatusDetails(specialist.status);
  
  return (
    <Card 
      className={clsx(styles.specialistDetail, className)}
      variant="elevated"
      accent="info"
    >
      <CardHeader 
        title={
          <div className={styles.headerTitleContainer}>
            {onBack && (
              <Button 
                variant="ghost" 
                size="sm" 
                leftIcon={<IconArrowLeft size={16} stroke={1.5} />}
                onClick={onBack}
                className={styles.backButton}
              >
                Back
              </Button>
            )}
            <span>{specialist.name}</span>
          </div>
        }
        subtitle={
          <div className={styles.headerSubtitle}>
            <Badge colorScheme="info" variant="subtle">
              {formatString(specialist.specialization)}
            </Badge>
            <Badge colorScheme={statusInfo.color} variant="subtle">
              {statusInfo.label}
            </Badge>
          </div>
        }
        action={
          <div className={styles.actions}>
            {specialist.status === 'available' && onHire && (
              <Button 
                variant="primary" 
                size="sm"
                leftIcon={<IconUserPlus size={16} stroke={1.5} />}
                onClick={() => onHire(specialist.id)}
              >
                Hire Specialist
              </Button>
            )}
            
            {specialist.status === 'hired' && onAssign && (
              <Button 
                variant="primary" 
                size="sm"
                leftIcon={<IconBriefcase size={16} stroke={1.5} />}
                onClick={() => onAssign(specialist.id)}
              >
                Assign to Mission
              </Button>
            )}
            
            {specialist.status === 'hired' && onTrain && (
              <Button 
                variant="secondary" 
                size="sm"
                leftIcon={<IconSchool size={16} stroke={1.5} />}
                onClick={() => onTrain(specialist.id)}
              >
                Train Skills
              </Button>
            )}
            
            {(specialist.status === 'hired' || specialist.status === 'on_mission' || specialist.status === 'training') && onFire && (
              <Button 
                variant="danger" 
                size="sm"
                leftIcon={<IconUserMinus size={16} stroke={1.5} />}
                onClick={() => onFire(specialist.id)}
              >
                Fire Specialist
              </Button>
            )}
          </div>
        }
      />
      
      <CardBody className={styles.content}>
        <div className={styles.profileHeader}>
          <div className={styles.avatarSection}>
            <div className={styles.avatar}>
              {specialist.avatar ? (
                <img src={specialist.avatar} alt={specialist.name} className={styles.avatarImage} />
              ) : (
                <div className={styles.initialsAvatar}>
                  <IconUser size={32} stroke={1.5} />
                </div>
              )}
            </div>
            <div className={styles.levelBadge}>
              <IconAward size={14} className={styles.levelIcon} stroke={1.5} />
              Level {specialist.level}
            </div>
          </div>
          
          <div className={styles.profileInfo}>
            <div className={styles.profileInfoRow}>
              <IconId size={16} className={styles.infoIcon} stroke={1.5} />
              <span className={styles.infoLabel}>Specialization:</span>
              <span className={styles.infoValue}>{formatString(specialist.specialization)}</span>
            </div>
            
            <div className={styles.profileInfoRow}>
              <IconCalendarEvent size={16} className={styles.infoIcon} stroke={1.5} />
              <span className={styles.infoLabel}>Hire Date:</span>
              <span className={styles.infoValue}>
                {specialist.hireDate ? formatDate(specialist.hireDate) : 'Not hired'}
              </span>
            </div>
            
            <div className={styles.profileInfoRow}>
              <IconBriefcase size={16} className={styles.infoIcon} stroke={1.5} />
              <span className={styles.infoLabel}>Cost:</span>
              <span className={styles.infoValue}>{specialist.cost} credits</span>
            </div>
            
            <div className={styles.profileInfoRow}>
              <IconChartBar size={16} className={styles.infoIcon} stroke={1.5} />
              <span className={styles.infoLabel}>Experience:</span>
              <span className={styles.infoValue}>{specialist.experience} XP</span>
            </div>
          </div>
        </div>
        
        <div className={styles.detailsGrid}>
          <Card className={`${styles.section} ${styles.skillsSection}`} variant="default">
            <CardHeader title={<>
              <IconChartBar size={18} className={styles.sectionIcon} stroke={1.5} /> Skills
            </>} />
            <CardBody>
              <div className={styles.skillList}>
                {topSkills.map(([skill, value]) => (
                  <div key={skill} className={styles.skillItem}>
                    <div className={styles.skillHeader}>
                      <span className={styles.skillName}>{formatString(skill)}</span>
                      <span className={styles.skillValue}>{value}</span>
                    </div>
                    <div className={styles.skillBar}>
                      <div 
                        className={styles.skillProgress} 
                        style={{ width: `${value}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
          
          <Card className={`${styles.section} ${styles.attributesSection}`} variant="default">
            <CardHeader title={<>
              <IconAward size={18} className={styles.sectionIcon} stroke={1.5} /> Attributes
            </>} />
            <CardBody>
              <div className={styles.attributeList}>
                <div className={styles.attributeItem}>
                  <span className={styles.attributeValue}>{specialist.attributes.reliability}</span>
                  <span className={styles.attributeName}>Reliability</span>
                </div>
                <div className={styles.attributeItem}>
                  <span className={styles.attributeValue}>{specialist.attributes.discretion}</span>
                  <span className={styles.attributeName}>Discretion</span>
                </div>
                <div className={styles.attributeItem}>
                  <span className={styles.attributeValue}>{specialist.attributes.efficiency}</span>
                  <span className={styles.attributeName}>Efficiency</span>
                </div>
                <div className={styles.attributeItem}>
                  <span className={styles.attributeValue}>{specialist.attributes.loyalty}</span>
                  <span className={styles.attributeName}>Loyalty</span>
                </div>
              </div>
            </CardBody>
          </Card>
          
          <Card className={`${styles.section} ${styles.traitsSection}`} variant="default">
            <CardHeader title={<>
              {specialist.traits.some(t => t.isPositive) ? 
                <IconThumbUp size={18} className={styles.sectionIcon} stroke={1.5} /> : 
                <IconThumbDown size={18} className={styles.sectionIcon} stroke={1.5} />} 
              Traits
            </>} />
            <CardBody>
              <div className={styles.traitList}>
                {specialist.traits.length > 0 ? (
                  specialist.traits.map((trait: Trait) => (
                    <div 
                      key={trait.type} 
                      className={`${styles.traitItem} ${trait.isPositive ? styles.traitPositive : styles.traitNegative}`}
                    >
                      <div className={styles.traitIcon}>
                        {trait.isPositive ? 
                          <IconThumbUp size={16} stroke={1.5} /> : 
                          <IconThumbDown size={16} stroke={1.5} />}
                      </div>
                      <div className={styles.traitInfo}>
                        <h4 className={
                          `${styles.traitName} ${
                            trait.isPositive ? styles.traitNamePositive : styles.traitNameNegative
                          }`
                        }>
                          {trait.name}
                        </h4>
                        <p className={styles.traitDescription}>{trait.description}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className={styles.emptyState}>
                    <p>No special traits identified</p>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
          
          <Card className={`${styles.section} ${styles.historySection}`} variant="default">
            <CardHeader title={<>
              <IconHistory size={18} className={styles.sectionIcon} stroke={1.5} /> Mission History
            </>} />
            <CardBody>
              {specialist.history && specialist.history.length > 0 ? (
                <>
                  <div className={styles.historyHeader}>
                    <span className={styles.historyHeaderItem}>Date</span>
                    <span className={styles.historyHeaderItem}>Mission</span>
                    <span className={styles.historyHeaderItem}>Role</span>
                    <span className={styles.historyHeaderItem}>Outcome</span>
                  </div>
                  <div className={styles.historyList}>
                    {specialist.history.map((history, index) => (
                      <div key={index} className={styles.historyItem}>
                        <span className={styles.historyDate}>{formatDate(history.date)}</span>
                        <span className={styles.historyMission}>{history.missionName}</span>
                        <span className={styles.historyRole}>Specialist</span>
                        <Badge 
                          colorScheme={
                            history.outcome === 'success' ? 'success' : 
                            history.outcome === 'partial' ? 'warning' : 'danger'
                          } 
                          variant="subtle"
                          size="sm"
                          className={styles.historyOutcome}
                        >
                          {history.outcome.charAt(0).toUpperCase() + history.outcome.slice(1)}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className={styles.emptyState}>
                  <IconClipboardList size={32} className={styles.emptyIcon} stroke={1.5} />
                  <p>No missions completed yet</p>
                </div>
              )}
            </CardBody>
          </Card>
        </div>
        
        {specialist.background && (
          <Card className={styles.backgroundSection} variant="default">
            <CardHeader title="Background" />
            <CardBody>
              <p className={styles.backgroundText}>{specialist.background}</p>
            </CardBody>
          </Card>
        )}
      </CardBody>
    </Card>
  );
};

export default SpecialistDetail;