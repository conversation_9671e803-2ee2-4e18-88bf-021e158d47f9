// Core domain types for Marketplace functionality
// This will be expanded as we implement the marketplace system

export type MarketType = 'public' | 'gray' | 'dark';
export type ItemCategory = 'software' | 'hardware' | 'infrastructure' | 'data' | 'service';
export type ItemRarity = 'common' | 'uncommon' | 'rare' | 'legendary';
export type ItemLegality = 'legal' | 'gray' | 'illegal';

export interface Item {
  id: string;
  name: string;
  description: string;
  category: ItemCategory;
  rarity: ItemRarity;
  legality: ItemLegality;
  cost: number;
  discount?: number; // Percentage discount if on sale
  requirements?: Requirement[];
  capabilities?: string[];
  marketSource: MarketType;
  vendorId: string;
  available: boolean;
}

export interface Requirement {
  type: 'reputation' | 'skill' | 'resource' | 'item' | 'level';
  id: string; // ID of faction, skill, resource, etc.
  value: number; // Required amount or level
}

export interface Vendor {
  id: string;
  name: string;
  description: string;
  marketType: MarketType;
  specialization?: ItemCategory[];
  reputation: number; // 0-100 scale
  items: string[]; // IDs of items sold
  relationshipLevel: number; // 0-5 scale
  accessRequirements?: Requirement[];
}

export interface Market {
  id: string;
  name: string;
  type: MarketType;
  accessRequirements: Requirement[];
  vendors: string[]; // Vendor IDs
  refreshInterval: number; // In game time units
  reputationImpact: Record<string, number>; // Impact on faction reputations
}
