.button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
  font-family: var(--font-main);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 1px solid transparent;
  gap: var(--space-xs); /* Add gap between icon and text */
}

/* <PERSON><PERSON> Variants */
.variant-primary {
  background: var(--neon-cyan);
  color: var(--cyberpunk-black);
  font-weight: bold;
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
}

.variant-primary:hover {
  background: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.8);
  transform: translateY(-2px);
}

.variant-primary:active {
  transform: translateY(1px);
  box-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
}

.variant-secondary {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.variant-secondary:hover {
  border-color: var(--border-active);
  background-color: var(--bg-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.variant-secondary:active {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}

.variant-danger {
  background: var(--neon-pink);
  color: var(--cyberpunk-black);
  border: none;
  font-weight: bold;
  box-shadow: 0 0 10px rgba(255, 0, 103, 0.5);
}

.variant-danger:hover {
  background: var(--neon-pink);
  box-shadow: 0 0 15px rgba(255, 0, 103, 0.7);
  transform: translateY(-2px);
}

.variant-danger:active {
  transform: translateY(1px);
  box-shadow: 0 0 5px rgba(255, 0, 103, 0.5);
}

.variant-ghost {
  background: transparent;
  color: var(--text-primary);
  border: none;
}

.variant-ghost:hover {
  background-color: rgba(255, 255, 255, 0.08);
  color: var(--text-accent);
  transform: translateY(-1px);
}

.variant-ghost:active {
  background-color: rgba(255, 255, 255, 0.12);
  transform: translateY(0);
}

/* New Cyberpunk variant */
.variant-cyberpunk {
  background: linear-gradient(135deg, #0d0d0d 0%, #1a1a1a 100%);
  color: var(--neon-cyan);
  border: 1px solid var(--neon-cyan);
  font-weight: bold;
  text-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.3), inset 0 0 5px rgba(0, 240, 255, 0.2);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.08em;
  backdrop-filter: blur(5px);
}

.variant-cyberpunk:before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--neon-cyan), transparent, var(--neon-pink));
  z-index: -1;
  animation: cyberpunk-border 3s linear infinite;
}

.variant-cyberpunk:hover {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.5), inset 0 0 10px rgba(0, 240, 255, 0.3);
  transform: translateY(-2px);
  color: #ffffff;
  border-color: #ffffff;
  text-shadow: 0 0 8px rgba(0, 240, 255, 0.8);
}

.variant-cyberpunk:active {
  transform: translateY(1px);
  box-shadow: 0 0 5px rgba(0, 240, 255, 0.3), inset 0 0 5px rgba(0, 240, 255, 0.2);
}

/* Button Sizes */
.size-sm {
  height: 30px;
  padding: 0 var(--space-sm);
  font-size: var(--font-size-xs);
}

.size-md {
  height: 36px;
  padding: 0 var(--space-md);
  font-size: var(--font-size-sm);
}

.size-lg {
  height: 44px;
  padding: 0 var(--space-lg);
  font-size: var(--font-size-md);
}

/* Full Width */
.fullWidth {
  width: 100%;
}

/* Loading State */
.loading {
  cursor: wait;
  opacity: 0.8;
}

.loading .content {
  visibility: hidden;
}

.spinner {
  position: absolute;
  width: 18px;
  height: 18px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
}

/* Disabled State */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  box-shadow: none;
}

.disabled:hover {
  transform: none;
  box-shadow: none;
  border-color: transparent;
}

/* Content container */
.content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
}

/* Icons */
.leftIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
  line-height: 0;
}

.rightIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
  line-height: 0;
}

/* Shine Effect */
.button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
  pointer-events: none;
}

.button:hover::after {
  left: 100%;
}

/* Focus state */
.button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 240, 255, 0.3);
}

.variant-primary:focus {
  box-shadow: 0 0 0 2px rgba(0, 240, 255, 0.5), 0 0 10px rgba(0, 240, 255, 0.5);
}

.variant-danger:focus {
  box-shadow: 0 0 0 2px rgba(255, 0, 103, 0.5), 0 0 10px rgba(255, 0, 103, 0.5);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes cyberpunk-border {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
