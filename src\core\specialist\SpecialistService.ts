/**
 * Service for managing specialists and teams
 */

import {
  Specialist,
  SpecializationType,
  SkillType,
  TraitType,
  Trait,
  Team,
  TeamMember,
  TrainingProgram,
  SpecialistStatus
} from './types';

import {
  SpecialistRepository,
  TeamRepository,
  TrainingRepository
} from './interfaces';

/**
 * Service for managing specialist hiring, team formation, and skill development
 */
export class SpecialistService {
  constructor(
    private specialistRepo: SpecialistRepository,
    private teamRepo: TeamRepository,
    private trainingRepo: TrainingRepository
  ) {}

  /**
   * Get all available specialists for hire
   */
  async getAvailableSpecialists() {
    return this.specialistRepo.getAvailableSpecialists();
  }

  /**
   * Get all specialists currently on the player's team
   */
  async getHiredSpecialists() {
    return this.specialistRepo.getHiredSpecialists();
  }

  /**
   * Get specialist by ID
   */
  async getSpecialistById(id: string) {
    return this.specialistRepo.getSpecialistById(id);
  }

  /**
   * Hire a specialist
   */
  async hireSpecialist(id: string) {
    return this.specialistRepo.hireSpecialist(id);
  }

  /**
   * Fire a specialist
   */
  async fireSpecialist(id: string) {
    return this.specialistRepo.fireSpecialist(id);
  }

  /**
   * Assign a specialist to a mission
   */
  async assignToMission(specialistId: string, missionId: string) {
    const specialist = await this.specialistRepo.getSpecialistById(specialistId);
    
    if (!specialist) {
      throw new Error(`Specialist with ID ${specialistId} not found`);
    }
    
    if (specialist.status !== 'hired') {
      throw new Error(`Specialist ${specialist.name} is not available for assignment`);
    }
    
    return this.specialistRepo.updateSpecialist(specialistId, {
      status: 'on_mission'
    });
  }

  /**
   * Return a specialist from a mission
   */
  async returnFromMission(
    specialistId: string, 
    missionId: string, 
    outcome: 'success' | 'failure' | 'partial',
    notes?: string
  ) {
    const specialist = await this.specialistRepo.getSpecialistById(specialistId);
    
    if (!specialist) {
      throw new Error(`Specialist with ID ${specialistId} not found`);
    }
    
    // Create mission history entry
    const historyEntry = {
      missionId,
      missionName: 'Mission', // This should come from mission service
      date: new Date().toISOString(),
      outcome,
      notes
    };
    
    const history = specialist.history ? [...specialist.history, historyEntry] : [historyEntry];
    
    // Update experience based on mission outcome
    let experienceGain = 0;
    switch (outcome) {
      case 'success':
        experienceGain = 100;
        break;
      case 'partial':
        experienceGain = 50;
        break;
      case 'failure':
        experienceGain = 25; // Still learn from failures
        break;
    }
    
    // Calculate new experience and potential level up
    const experience = specialist.experience + experienceGain;
    const level = this.calculateLevel(experience);
    
    return this.specialistRepo.updateSpecialist(specialistId, {
      status: 'hired',
      history,
      experience,
      level
    });
  }

  /**
   * Start specialist training
   */
  async startTraining(specialistId: string, programId: string) {
    const specialist = await this.specialistRepo.getSpecialistById(specialistId);
    const program = await this.trainingRepo.getProgramById(programId);
    
    if (!specialist) {
      throw new Error(`Specialist with ID ${specialistId} not found`);
    }
    
    if (!program) {
      throw new Error(`Training program with ID ${programId} not found`);
    }
    
    if (specialist.status !== 'hired') {
      throw new Error(`Specialist ${specialist.name} is not available for training`);
    }
    
    if (program.requiredLevel && specialist.level < program.requiredLevel) {
      throw new Error(`Specialist ${specialist.name} does not meet the required level for this training`);
    }
    
    await this.trainingRepo.enrollSpecialist(specialistId, programId);
    
    return this.specialistRepo.updateSpecialist(specialistId, {
      status: 'training'
    });
  }

  /**
   * Complete specialist training
   */
  async completeTraining(specialistId: string, programId: string) {
    return this.trainingRepo.completeTraining(specialistId, programId);
  }

  /**
   * Create a new team
   */
  async createTeam(name: string, memberIds: string[]) {
    // Validate that all members exist and are available
    for (const id of memberIds) {
      const specialist = await this.specialistRepo.getSpecialistById(id);
      
      if (!specialist) {
        throw new Error(`Specialist with ID ${id} not found`);
      }
      
      if (specialist.status !== 'hired') {
        throw new Error(`Specialist ${specialist.name} is not available for team assignment`);
      }
    }
    
    return this.teamRepo.createTeam(name, memberIds);
  }

  /**
   * Get all teams
   */
  async getAllTeams() {
    return this.teamRepo.getAllTeams();
  }

  /**
   * Get team by ID
   */
  async getTeamById(id: string) {
    return this.teamRepo.getTeamById(id);
  }

  /**
   * Update a team
   */
  async updateTeam(id: string, updates: Partial<Team>) {
    return this.teamRepo.updateTeam(id, updates);
  }

  /**
   * Delete a team
   */
  async deleteTeam(id: string) {
    return this.teamRepo.deleteTeam(id);
  }

  /**
   * Generate new specialists for the marketplace
   */
  async generateNewSpecialists(count: number) {
    return this.specialistRepo.generateSpecialists(count);
  }

  /**
   * Calculate level based on experience
   * Private helper method
   */
  private calculateLevel(experience: number): number {
    // Simple level calculation: each level requires 500 * current level experience
    // Level 1: 0-499
    // Level 2: 500-1499
    // Level 3: 1500-2999
    // etc.
    let level = 1;
    let threshold = 500;
    
    while (experience >= threshold) {
      level++;
      threshold += 500 * level;
    }
    
    return level;
  }
}
