import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LayoutType, Tab, TabId, TabType } from '@/shared/types/common';
import { TabsState } from './types';

const initialState: TabsState = {
  tabs: [
    {
      id: 'dashboard',
      title: 'Dashboard',
      closable: false,
      type: 'dashboard',
    },
  ],
  activeTabId: 'dashboard',
  layout: 'single',
};

export const tabsSlice = createSlice({
  name: 'tabs',
  initialState,
  reducers: {
    addTab: (state, action: PayloadAction<Tab>) => {
      // Check if tab already exists
      const existingTabIndex = state.tabs.findIndex(tab => 
        tab.type === action.payload.type && 
        JSON.stringify(tab.data) === JSON.stringify(action.payload.data)
      );
      
      if (existingTabIndex !== -1) {
        // If tab exists, just set it as active
        state.activeTabId = state.tabs[existingTabIndex].id;
      } else {
        // Add new tab
        state.tabs.push(action.payload);
        state.activeTabId = action.payload.id;
      }
      
      // Update layout based on number of tabs
      state.layout = getLayoutForTabCount(state.tabs.length);
    },
    
    closeTab: (state, action: PayloadAction<TabId>) => {
      const tabIndex = state.tabs.findIndex(tab => tab.id === action.payload);
      
      if (tabIndex !== -1 && state.tabs[tabIndex].closable) {
        // If we're closing the active tab, set a new active tab
        if (state.activeTabId === action.payload) {
          // Try to activate the tab to the left, or the first tab if there isn't one
          const newActiveIndex = tabIndex > 0 ? tabIndex - 1 : 0;
          state.activeTabId = state.tabs[newActiveIndex]?.id || null;
        }
        
        // Remove the tab
        state.tabs = state.tabs.filter(tab => tab.id !== action.payload);
        
        // Update layout based on new tab count
        state.layout = getLayoutForTabCount(state.tabs.length);
      }
    },
    
    setActiveTab: (state, action: PayloadAction<TabId>) => {
      const tabExists = state.tabs.some(tab => tab.id === action.payload);
      if (tabExists) {
        state.activeTabId = action.payload;
      }
    },
    
    setLayout: (state, action: PayloadAction<LayoutType>) => {
      state.layout = action.payload;
    },
  },
});

// Helper function to determine layout based on tab count
function getLayoutForTabCount(count: number): LayoutType {
  if (count <= 1) return 'single';
  if (count === 2) return 'split';
  if (count === 3) return 'triple';
  return 'grid';
}

export const { addTab, closeTab, setActiveTab, setLayout } = tabsSlice.actions;

export default tabsSlice.reducer;
