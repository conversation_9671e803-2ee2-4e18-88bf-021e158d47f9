import React, { useState, useRef, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../../app/store';
import { executeCommand, setCommandOutput, clearTerminal } from '../model/slice';
import { processCommand } from '../lib/commandProcessor';
import styles from './TerminalConsole.module.css';
import { IconTerminal2, IconChevronRight, IconEraser, IconDownload, IconSettings } from '@tabler/icons-react';
import clsx from 'clsx';

interface TerminalConsoleProps {
  instanceId?: string;
  className?: string;
}

interface CommandOutputProps {
  command: string;
  output: string;
  status: 'success' | 'error' | 'info' | 'warning';
}

const CommandOutput: React.FC<CommandOutputProps> = ({ command, output, status }) => {
  return (
    <div className={styles.commandOutput}>
      <div className={styles.commandText}>&gt; {command}</div>
      <div className={clsx(styles.outputText, {
        [styles.errorText]: status === 'error',
        [styles.successText]: status === 'success',
        [styles.warningText]: status === 'warning',
        [styles.infoText]: status === 'info',
      })}>
        {output}
      </div>
    </div>
  );
};

const TerminalConsole: React.FC<TerminalConsoleProps> = ({ 
  instanceId = 'default',
  className
}) => {
  const dispatch = useAppDispatch();
  const { commands, outputs } = useAppSelector(state => state.terminal);
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const consoleEndRef = useRef<HTMLDivElement>(null);
  
  // Focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  
  // Scroll to bottom when outputs change
  useEffect(() => {
    if (consoleEndRef.current) {
      consoleEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [outputs]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputValue.trim()) return;
    
    // Create a unique ID for this command
    const commandId = Date.now().toString();
    
    // Dispatch command to store
    dispatch(executeCommand({ command: inputValue, instanceId, commandId }));
    
    // Process the command and get output
    const { output, status } = await processCommand(inputValue, '/home/<USER>', instanceId);
    
    // Update with command output
    dispatch(setCommandOutput({
      commandId,
      output,
      status,
      instanceId,
    }));
    
    // Clear input
    setInputValue('');
  };
  
  const handleClear = () => {
    dispatch(clearTerminal({ instanceId }));
  };
  
  // Filter commands for this instance
  const instanceCommands = commands.filter(cmd => cmd.instanceId === instanceId);
  const instanceOutputs = outputs.filter(out => out.instanceId === instanceId);
  
  return (
    <div 
      className={clsx(styles.terminalConsole, className)} 
      onClick={() => inputRef.current?.focus()}
    >
      <div className={styles.toolbarArea}>
        <button className={styles.toolbarButton} onClick={handleClear} title="Clear terminal">
          <IconEraser size={16} stroke={1.5} />
        </button>
        <button className={styles.toolbarButton} title="Export log">
          <IconDownload size={16} stroke={1.5} />
        </button>
        <button className={styles.toolbarButton} title="Terminal settings">
          <IconSettings size={16} stroke={1.5} />
        </button>
      </div>
      
      <div className={styles.outputArea}>
        {/* Welcome message */}
        <div className={styles.welcomeMessage}>
          <div className={styles.systemLine}>
            <IconTerminal2 size={18} className={styles.icon} stroke={1.5} />
            CyberClash Terminal v1.0.0
          </div>
          <div className={styles.systemLine}>
            Type 'help' for a list of commands.
          </div>
        </div>
        
        {/* Command history and outputs */}
        {instanceCommands.map((cmd, index) => {
          const output = instanceOutputs.find(out => out.commandId === cmd.commandId);
          if (!output) return null;
          
          return (
            <CommandOutput 
              key={cmd.commandId}
              command={cmd.command}
              output={output.output}
              status={output.status}
            />
          );
        })}
        
        {/* Auto-scroll anchor */}
        <div ref={consoleEndRef} />
      </div>
      
      {/* Command input */}
      <form onSubmit={handleSubmit} className={styles.commandForm}>
        <span className={styles.prompt}>
          <IconChevronRight size={16} className={styles.promptIcon} stroke={1.5} />
          /home/<USER>
        </span>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          className={styles.commandInput}
          spellCheck={false}
          autoComplete="off"
          autoCapitalize="off"
          placeholder="Enter command..."
        />
      </form>
    </div>
  );
};

export default TerminalConsole;
