:root {
  /* Cyberpunk Core Color Palette */
  --cyberpunk-black: #0a0a12;
  --cyberpunk-dark: #0c1221;
  --cyberpunk-darker: #131a29;
  --cyberpunk-midnight: #1a1c2c;
  --cyberpunk-deepblue: #212746;
  
  /* Neon Accent Colors */
  --neon-cyan: #00f0ff;
  --neon-pink: #ff0067;
  --neon-purple: #9e00ff;
  --neon-yellow: #ffdf00;
  --neon-green: #00ff9f;
  --neon-orange: #ff9000;
  --neon-blue: #0088ff;
  
  /* Muted Versions for Backgrounds and Subtle Elements */
  --neon-cyan-muted: rgba(0, 240, 255, 0.2);
  --neon-pink-muted: rgba(255, 0, 103, 0.2);
  --neon-purple-muted: rgba(158, 0, 255, 0.2);
  --neon-yellow-muted: rgba(255, 223, 0, 0.2);
  --neon-green-muted: rgba(0, 255, 159, 0.2);
  
  /* Gradients */
  --gradient-primary: linear-gradient(90deg, var(--neon-cyan), var(--neon-purple));
  --gradient-secondary: linear-gradient(90deg, var(--neon-pink), var(--neon-purple));
  --gradient-warn: linear-gradient(90deg, var(--neon-yellow), var(--neon-pink));
  --gradient-success: linear-gradient(90deg, var(--neon-cyan), var(--neon-green));
  --gradient-info: linear-gradient(90deg, var(--neon-blue), var(--neon-cyan));
  --gradient-dark: linear-gradient(135deg, var(--cyberpunk-midnight), var(--cyberpunk-darker));
  
  /* UI Element Colors */
  --bg-primary: var(--cyberpunk-black);
  --bg-secondary: var(--cyberpunk-dark);
  --bg-tertiary: var(--cyberpunk-darker);
  --bg-elevated: var(--cyberpunk-midnight);
  --bg-accent: var(--neon-purple);
  --bg-hover: var(--cyberpunk-deepblue);
  
  --bg-card: rgba(33, 39, 70, 0.5);
  --bg-modal: rgba(26, 28, 44, 0.9);
  --bg-overlay: rgba(10, 10, 18, 0.8);
  --bg-tooltip: rgba(0, 240, 255, 0.1);
  
  --text-primary: rgba(255, 255, 255, 0.9);
  --text-secondary: rgba(255, 255, 255, 0.6);
  --text-muted: rgba(255, 255, 255, 0.4);
  --text-accent: var(--neon-cyan);
  --text-warning: var(--neon-yellow);
  --text-danger: var(--neon-pink);
  --text-success: var(--neon-green);
  --text-info: var(--neon-blue);
  --text-on-accent: #ffffff;
  
  /* Borders */
  --border-color: rgba(0, 240, 255, 0.15);
  --border-glow: 0 0 5px rgba(0, 240, 255, 0.3);
  --border-active: rgba(0, 240, 255, 0.5);
  --border-width: 1px;
  --border-style: solid;
  
  /* Glitch Effect Colors */
  --glitch-cyan: rgba(0, 240, 255, 0.7);
  --glitch-pink: rgba(255, 0, 103, 0.7);
  --glitch-yellow: rgba(255, 223, 0, 0.7);
  
  /* Status colors */
  --color-success: var(--neon-green);
  --color-error: var(--neon-pink);
  --color-warning: var(--neon-yellow);
  --color-info: var(--neon-cyan);
  
  /* UI Dimensions */
  --header-height: 48px;
  --footer-height: 56px;
  --tab-height: 36px;
  --sidebar-width: 220px;
  --card-padding: 16px;
  
  /* Shadows */
  --shadow-sm: 0 2px 8px rgba(0, 240, 255, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 240, 255, 0.15), 0 0 5px rgba(0, 240, 255, 0.1);
  --shadow-lg: 0 8px 24px rgba(0, 240, 255, 0.2), 0 0 10px rgba(0, 240, 255, 0.1);
  --shadow-neon: 0 0 10px rgba(0, 240, 255, 0.5), 0 0 20px rgba(0, 240, 255, 0.2);
  --shadow-pink: 0 0 10px rgba(255, 0, 103, 0.5), 0 0 20px rgba(255, 0, 103, 0.2);
  --shadow-purple: 0 0 10px rgba(158, 0, 255, 0.5), 0 0 20px rgba(158, 0, 255, 0.2);
  --shadow-yellow: 0 0 10px rgba(255, 223, 0, 0.5), 0 0 20px rgba(255, 223, 0, 0.2);
  --shadow-green: 0 0 10px rgba(0, 255, 159, 0.5), 0 0 20px rgba(0, 255, 159, 0.2);
  
  /* Animations */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --animation-glitch: 0.3s ease;
  --animation-pulse: 2s ease infinite;
  --animation-flicker: 3s linear infinite;
  
  /* Borders and Shapes */
  --border-radius-sm: 3px;
  --border-radius-md: 5px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  
  /* Font Families */
  --font-main: 'Oxanium', 'Rajdhani', sans-serif;
  --font-mono: 'PT Mono', 'Share Tech Mono', monospace;
  --font-display: 'Oxanium', 'Orbitron', sans-serif;
  
  /* Font Sizes */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-md: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  
  /* Line Heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.8;
  
  /* Spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  
  /* Z-indices */
  --z-dropdown: 1000;
  --z-topbar: 1100;
  --z-bottombar: 1100;
  --z-modal: 1200;
  --z-notification: 1300;
  --z-tooltip: 1400;
}
