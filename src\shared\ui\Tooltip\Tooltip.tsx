import React, { useState, useRef, useEffect } from 'react';
import styles from './Tooltip.module.css';
import clsx from 'clsx';

export type TooltipPlacement = 'top' | 'bottom' | 'left' | 'right';
export type TooltipVariant = 'default' | 'info' | 'success' | 'warning' | 'danger';

export interface TooltipProps {
  /** Content to display inside the tooltip */
  content: React.ReactNode;
  /** Tooltip placement */
  placement?: TooltipPlacement;
  /** Visual style of the tooltip */
  variant?: TooltipVariant;
  /** Delay before showing the tooltip (ms) */
  delay?: number;
  /** Whether the tooltip is always visible */
  alwaysVisible?: boolean;
  /** Custom classname for the tooltip container */
  className?: string;
  /** Custom classname for the tooltip content */
  tooltipClassName?: string;
  /** Children element that triggers the tooltip */
  children: React.ReactElement;
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  placement = 'top',
  variant = 'default',
  delay = 200,
  alwaysVisible = false,
  className,
  tooltipClassName,
  children,
}) => {
  const [isVisible, setIsVisible] = useState(alwaysVisible);
  const [tooltipPosition, setTooltipPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<number | null>(null);

  const showTooltip = () => {
    if (timeoutRef.current) {
      window.clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = window.setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      window.clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (alwaysVisible) {
      setIsVisible(true);
    }
  }, [alwaysVisible]);

  useEffect(() => {
    if (!isVisible || !triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();

    let top = 0;
    let left = 0;

    switch (placement) {
      case 'top':
        top = triggerRect.top - tooltipRect.height - 8;
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
        break;
      case 'bottom':
        top = triggerRect.bottom + 8;
        left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
        break;
      case 'left':
        top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
        left = triggerRect.left - tooltipRect.width - 8;
        break;
      case 'right':
        top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
        left = triggerRect.right + 8;
        break;
    }

    // Adjust position to keep tooltip within viewport
    const boundingRect = document.body.getBoundingClientRect();
    
    if (left < 0) left = 8;
    if (top < 0) top = 8;
    if (left + tooltipRect.width > window.innerWidth) {
      left = window.innerWidth - tooltipRect.width - 8;
    }
    if (top + tooltipRect.height > window.innerHeight) {
      top = window.innerHeight - tooltipRect.height - 8;
    }

    setTooltipPosition({ top, left });
  }, [isVisible, placement]);

  const clonedChild = React.cloneElement(children, {
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip();
      if (children.props.onMouseEnter) {
        children.props.onMouseEnter(e);
      }
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip();
      if (children.props.onMouseLeave) {
        children.props.onMouseLeave(e);
      }
    },
    onFocus: (e: React.FocusEvent) => {
      showTooltip();
      if (children.props.onFocus) {
        children.props.onFocus(e);
      }
    },
    onBlur: (e: React.FocusEvent) => {
      hideTooltip();
      if (children.props.onBlur) {
        children.props.onBlur(e);
      }
    },
  });

  return (
    <div className={clsx(styles.tooltipWrapper, className)} ref={triggerRef}>
      {clonedChild}
      {isVisible && (
        <div 
          className={clsx(
            styles.tooltip, 
            styles[placement], 
            styles[variant],
            tooltipClassName
          )} 
          ref={tooltipRef}
          style={{
            top: `${tooltipPosition.top}px`,
            left: `${tooltipPosition.left}px`,
            position: 'fixed',
            zIndex: 9999,
          }}
        >
          <div className={styles.tooltipContent}>{content}</div>
          <div className={styles.tooltipArrow} />
        </div>
      )}
    </div>
  );
};

export default Tooltip;