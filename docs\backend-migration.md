# Backend Migration Guide

## Overview

This document outlines the process for migrating CyberClash from a client-only application to a client-server architecture. The Core Layer architecture was designed specifically to make this transition smooth by separating game logic from UI and state management.

## Migration Strategy

### Phase 1: Core Layer Preparation

1. **Review interfaces**
   - Ensure all repository interfaces are properly defined
   - Update service interfaces to be async-first
   - Check for any client-specific dependencies in core logic

2. **Extract core modules**
   - Move core modules to a separate package
   - Ensure they have no frontend dependencies
   - Create a clean API boundary

3. **Standardize data models**
   - Review and standardize all type definitions
   - Add serialization/deserialization support
   - Define clear validation rules

### Phase 2: Backend Development

1. **Setup server environment**
   - Choose a backend technology (Node.js/Express recommended)
   - Set up project structure following core domains
   - Configure development environment

2. **Implement repositories**
   - Create database schema for each domain
   - Implement repository interfaces with database connections
   - Add data validation and error handling

3. **Migrate core services**
   - Move service implementations to the backend
   - Adapt any platform-specific code
   - Add authentication and authorization

4. **Create API endpoints**
   - Define RESTful or GraphQL API
   - Map endpoints to core service methods
   - Implement request/response handling

### Phase 3: Frontend Adaptation

1. **Create API clients**
   - Implement API service layer
   - Handle authentication and tokens
   - Add request/response interceptors

2. **Update repositories**
   - Replace mock repositories with API-based implementations
   - Keep the same interface contracts
   - Add offline caching where appropriate

3. **Adapt state management**
   - Update Redux actions to work with API
   - Add loading states and error handling
   - Implement optimistic updates where applicable

## Implementation Details

### Repository Pattern Example

#### Current Mock Repository

```typescript
// src/features/mission/repository/index.ts
export class MockMissionRepository implements MissionRepository {
  private missions: Record<string, Mission> = {};
  
  async getAvailableMissions(): Promise<Mission[]> {
    return Object.values(this.missions).filter(m => m.status === 'available');
  }
  
  // Other methods...
}
```

#### Future API Repository

```typescript
// src/features/mission/repository/index.ts
export class ApiMissionRepository implements MissionRepository {
  private apiClient: ApiClient;
  
  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }
  
  async getAvailableMissions(): Promise<Mission[]> {
    const response = await this.apiClient.get('/missions?status=available');
    return response.data;
  }
  
  // Other methods...
}
```

### Service Initialization

#### Current Initialization

```typescript
// src/core/mission/index.ts
import { MissionService } from './MissionService';
import { MockMissionRepository } from '../../features/mission/repository';

// Create repository
const missionRepository = new MockMissionRepository();

// Create and export mission service
export const missionService = new MissionService(missionRepository);
```

#### Future Initialization

```typescript
// src/core/mission/index.ts
import { MissionService } from './MissionService';
import { ApiMissionRepository } from '../../features/mission/repository';
import { apiClient } from '../../app/api';

// Create repository with API client
const missionRepository = new ApiMissionRepository(apiClient);

// Create and export mission service
export const missionService = new MissionService(missionRepository);
```

## API Design

The API will follow RESTful principles with these endpoints for each domain:

### Missions

- `GET /missions` - List missions with filters
- `GET /missions/:id` - Get mission details
- `POST /missions/:id/start` - Start a mission
- `POST /missions/:id/objectives/:objectiveId/complete` - Complete an objective
- `GET /missions/:id/progress` - Get mission progress

### Specialists

- `GET /specialists` - List available specialists
- `GET /specialists/hired` - List hired specialists
- `POST /specialists/:id/hire` - Hire a specialist
- `POST /specialists/:id/assign` - Assign to mission

### Market

- `GET /markets` - List available markets
- `GET /markets/:id/items` - List items in a market
- `POST /items/:id/purchase` - Purchase an item

## Authentication

The API will use JWT (JSON Web Tokens) for authentication:

1. Client authenticates with credentials
2. Server returns JWT token
3. Client includes token in subsequent requests
4. Server validates token for each request

## Migration Timeline

1. **Preparation (1-2 weeks)**
   - Review and update interfaces
   - Document API requirements
   - Plan database schema

2. **Backend Development (3-4 weeks)**
   - Set up server infrastructure
   - Implement core repositories and services
   - Create API endpoints and authentication

3. **Frontend Adaptation (2-3 weeks)**
   - Create API client
   - Update repository implementations
   - Adapt Redux actions
   - Add loading and error states

4. **Testing and Refinement (2 weeks)**
   - End-to-end testing
   - Performance optimization
   - Security auditing
   - Bug fixing

## Challenges and Considerations

- **Offline Support**: Consider strategies for offline play
- **Performance**: Monitor API performance and optimize where needed
- **Security**: Implement proper authentication and authorization
- **Versioning**: Plan for API versioning to support updates
- **Testing**: Create comprehensive tests for API endpoints

By following this migration plan, we can transition CyberClash to a client-server architecture while minimizing disruption to the development process.
