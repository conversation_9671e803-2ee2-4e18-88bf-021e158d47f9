/**
 * Specialist feature types
 */

import { Specialist, Team, TrainingProgram } from '@/core/specialist/types';

/**
 * Specialist state model for Zustand store
 */
export interface SpecialistState {
  specialists: Record<string, Specialist>;
  availableSpecialists: string[];
  hiredSpecialists: string[];
  teams: Record<string, Team>;
  trainingPrograms: Record<string, TrainingProgram>;
  activeSpecialistId: string | null;
  activeTeamId: string | null;
  isLoading: boolean;
  error: string | null;
}
