/**
 * Specialist feature types
 */

import { Specialist, Team, TrainingProgram } from '@/core/specialist/types';

// /**
//  * Specialist state model for Redux
//  */
export interface SpecialistState {
  specialists: Record<string, Specialist>;
  availableSpecialists: string[];
  hiredSpecialists: string[];
  teams: Record<string, Team>;
  trainingPrograms: Record<string, TrainingProgram>;
  activeSpecialistId: string | null;
  activeTeamId: string | null;
  isLoading: boolean;
  error: string | null;
}

/**
 * Thunk payload types
 */
export interface FetchSpecialistsPayload {
  status?: 'available' | 'hired' | 'all';
}

export interface HireSpecialistPayload {
  specialistId: string;
}

export interface FireSpecialistPayload {
  specialistId: string;
}

export interface AssignToMissionPayload {
  specialistId: string;
  missionId: string;
}

export interface ReturnFromMissionPayload {
  specialistId: string;
  missionId: string;
  outcome: 'success' | 'failure' | 'partial';
  notes?: string;
}

export interface StartTrainingPayload {
  specialistId: string;
  programId: string;
}

export interface CompleteTrainingPayload {
  specialistId: string;
  programId: string;
}

export interface CreateTeamPayload {
  name: string;
  memberIds: string[];
}

export interface UpdateTeamPayload {
  teamId: string;
  updates: Partial<Team>;
}
