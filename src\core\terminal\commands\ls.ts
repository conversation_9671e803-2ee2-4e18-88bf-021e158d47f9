import { Command, CommandResult, CommandContext } from '../types';
import { FileSystemRepository } from '../../common/repositories/interfaces';

export function lsCommand(fileSystemRepo: FileSystemRepository): Command {
  return {
    help: 'Lists files and directories.\nUsage: ls [directory]',
    
    execute(args: string[], context: CommandContext): CommandResult {
      // Determine which directory to list
      let targetDir = context.currentDirectory;
      
      if (args.length > 0) {
        // Handle relative or absolute paths
        targetDir = fileSystemRepo.resolvePath(context.currentDirectory, args[0]);
      }
      
      // Check if directory exists
      const dirEntry = fileSystemRepo.getEntry(targetDir);
      
      if (!dirEntry || dirEntry.type !== 'directory') {
        return {
          output: `ls: cannot access '${args[0] || targetDir}': No such directory`,
          status: 'error'
        };
      }
      
      // Get child entries
      const childPaths = dirEntry.children || [];
      const items = childPaths.map(path => {
        const entry = fileSystemRepo.getEntry(path);
        return {
          name: entry?.name || path.split('/').pop() || '',
          type: entry?.type || 'file'
        };
      });
      
      // Format output with colors (directories in blue)
      let output = '';
      
      if (items.length === 0) {
        output = '// Directory is empty';
      } else {
        // Organize items into columns
        const maxItemLength = Math.max(...items.map(item => item.name.length)) + 2;
        const columns = Math.floor(80 / maxItemLength) || 1;
        
        for (let i = 0; i < items.length; i += columns) {
          const row = items.slice(i, i + columns);
          
          row.forEach(item => {
            if (item.type === 'directory') {
              // Blue for directories
              output += `\x1b[34m${item.name}/\x1b[0m`.padEnd(maxItemLength);
            } else {
              // Regular color for files
              output += item.name.padEnd(maxItemLength);
            }
          });
          
          output += '\n';
        }
      }
      
      return { output: output.trim(), status: 'success' };
    }
  };
}
