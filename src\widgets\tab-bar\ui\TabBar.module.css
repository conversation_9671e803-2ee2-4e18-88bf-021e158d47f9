/* Tab Bar - horizontal bar containing the tabs */
.tabBar {
  display: flex;
  align-items: center;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  height: var(--tab-height);
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Hide scrollbar for Chrome, Safari and Opera */
.tabBar::-webkit-scrollbar {
  height: 3px;
}

.tabBar::-webkit-scrollbar-track {
  background: transparent;
}

.tabBar::-webkit-scrollbar-thumb {
  background-color: var(--neon-cyan-muted);
  border-radius: 10px;
}

/* Container for all tabs */
.tabsList {
  display: flex;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
}

.tabsList::-webkit-scrollbar {
  display: none;
}

/* Individual tab */
.tab {
  display: flex;
  align-items: center;
  height: 100%;
  min-width: 120px;
  max-width: 200px;
  padding: 0 var(--space-sm);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-right: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  position: relative;
  overflow: hidden;
}

/* Active tab styling */
.tab.active {
  background-color: var(--bg-primary);
  color: var(--text-accent);
}

/* Tab hover effect */
.tab:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  box-shadow: inset 0 0 8px rgba(0, 240, 255, 0.1);
}

/* Effect for the active tab */
.tab.active::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--gradient-primary);
  box-shadow: 0 0 8px rgba(0, 240, 255, 0.5);
}

/* Tab icon */
.tabIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-xs);
  font-size: 16px;
  min-width: 16px;
}

/* Tab title */
.tabTitle {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: var(--font-size-sm);
  margin-right: var(--space-sm);
}

/* Close button */
.closeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: transparent;
  border: none;
  color: var(--text-muted);
  font-size: 14px;
  padding: 0;
  margin-left: auto;
  cursor: pointer;
  transition: all var(--transition-fast);
  opacity: 0.5;
}

.closeButton:hover {
  background-color: rgba(255, 0, 103, 0.2);
  color: var(--neon-pink);
  opacity: 1;
  box-shadow: 0 0 5px rgba(255, 0, 103, 0.4);
}

/* Add tab button */
.addTabButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  min-width: 24px;
  border-radius: 50%;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 18px;
  margin-left: var(--space-sm);
  margin-right: var(--space-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.addTabButton:hover {
  background-color: var(--neon-cyan-muted);
  color: var(--text-accent);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 8px rgba(0, 240, 255, 0.3);
  transform: scale(1.05);
}

/* Data shimmer animation for active tab */
.tab.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  background-size: 200% 100%;
  animation: dataflow 2s linear infinite;
}

@keyframes dataflow {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
