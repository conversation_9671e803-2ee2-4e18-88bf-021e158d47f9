import { CommandResult, CommandContext } from './types';
import { FileSystemRepository, TerminalRepository } from '../common/repositories/interfaces';
import { TerminalCommands } from './commands';

export class TerminalService {
  private commands: Record<string, any>;

  constructor(
    private fileSystemRepo: FileSystemRepository,
    private terminalRepo: TerminalRepository
  ) {
    // Initialize available commands
    this.commands = TerminalCommands.createCommands(fileSystemRepo);
  }

  executeCommand(commandString: string, instanceId: string): CommandResult {
    // Get current directory from repository
    const currentDirectory = this.terminalRepo.getCurrentDirectory(instanceId);
    
    // Add command to history
    this.terminalRepo.addCommandToHistory(instanceId, commandString);
    
    // Parse the command
    const parts = commandString.trim().split(/\s+/);
    const commandName = parts[0].toLowerCase();
    const args = parts.slice(1);
    
    // Create context object
    const context: CommandContext = {
      currentDirectory,
      environmentVariables: {
        // Example environment variables
        'USER': 'operator',
        'HOME': '/home/<USER>',
        'PATH': '/bin:/usr/bin',
      }
    };
    
    // Execute command if it exists
    if (this.commands[commandName]) {
      const result = this.commands[commandName].execute(args, context);
      
      // If command changes directory (like cd), update in repository
      if (result.newDirectory) {
        this.terminalRepo.setCurrentDirectory(instanceId, result.newDirectory);
      }
      
      return result;
    }
    
    // Command not found
    return {
      output: `Command not found: ${commandName}. Type 'help' for a list of available commands.`,
      status: 'error'
    };
  }

  getAvailableCommands(): string[] {
    return Object.keys(this.commands);
  }

  getCommandHelp(commandName: string): string {
    return this.commands[commandName]?.help || 'No help available for this command.';
  }
}
