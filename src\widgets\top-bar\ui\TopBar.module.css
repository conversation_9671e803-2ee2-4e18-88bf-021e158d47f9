.topBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: var(--header-height);
  background-color: var(--bg-elevated);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--space-md);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: var(--z-topbar);
  position: relative;
}

.brand {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.logo {
  font-size: 1.4em;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neon-cyan);
  animation: pulse var(--animation-pulse);
}

.name {
  font-family: var(--font-display);
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.widgetsArea {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  padding: 0 var(--space-lg);
}

.widget {
  height: 100%;
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  transition: color var(--transition-fast);
}

.widget:hover {
  color: var(--text-primary);
}

/* Specific widgets styling */
.timeWidget,
.dateWidget,
.securityWidget,
.creditsWidget {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.actionArea {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-md);
  background: transparent;
  border: 1px solid transparent;
  color: var(--text-secondary);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
}

.actionButton:hover {
  color: var(--text-primary);
  background: rgba(0, 240, 255, 0.1);
  border-color: var(--border-color);
}

.actionButton.active {
  color: var(--text-accent);
  background: rgba(0, 240, 255, 0.15);
  border-color: var(--border-active);
  box-shadow: 0 0 8px rgba(0, 240, 255, 0.3);
}

.iconWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon {
  font-size: 18px;
}

.notificationDot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--neon-pink);
  box-shadow: 0 0 4px rgba(255, 0, 103, 0.6);
  animation: pulse 2s infinite;
}

/* Configuration Panel */
.configPanel {
  position: absolute;
  top: 100%;
  right: 20px;
  width: 250px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--space-md);
  z-index: var(--z-dropdown);
  animation: fadeIn 0.2s ease-out;
}

.configPanel h3 {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--font-size-md);
  color: var(--text-accent);
  text-align: center;
}

.widgetList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.widgetItem {
  margin-bottom: var(--space-sm);
  padding: var(--space-xs) 0;
  border-bottom: 1px solid var(--border-color);
}

.widgetToggle {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  cursor: pointer;
}

.widgetToggle input[type="checkbox"] {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background-color: var(--bg-tertiary);
  position: relative;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.widgetToggle input[type="checkbox"]:checked {
  background-color: var(--neon-cyan);
  border-color: var(--neon-cyan);
}

.widgetToggle input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 5px;
  width: 4px;
  height: 8px;
  border: solid var(--cyberpunk-black);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.widgetName {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
