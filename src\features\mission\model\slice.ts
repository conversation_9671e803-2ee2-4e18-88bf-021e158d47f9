import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  MissionState,
  FetchMissionsPayload,
  MissionStartedPayload,
  MissionProgressUpdatedPayload,
  MissionCompletedPayload,
  MissionFailedPayload,
  ObjectiveCompletedPayload,
  ObjectiveFailedPayload
} from './types';
import {
  Mission,
  MissionProgress
} from '@/core/mission/types';
import { missionService } from '@/core/mission';

// Initial state
const initialState: MissionState = {
  missions: {},
  availableMissions: [],
  inProgressMissions: [],
  completedMissions: [],
  failedMissions: [],
  activeMissionId: null,
  missionProgress: {},
  isLoading: false,
  error: null
};

// Async thunks
export const fetchMissions = createAsyncThunk(
  'mission/fetchMissions',
  async ({ filter }: FetchMissionsPayload, { rejectWithValue }) => {
    try {
      const missions = await missionService.getAvailableMissions(filter);
      return missions;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch missions');
    }
  }
);

export const fetchMissionById = createAsyncThunk(
  'mission/fetchMissionById',
  async (missionId: string, { rejectWithValue }) => {
    try {
      const mission = await missionService.getMissionById(missionId);
      if (!mission) {
        throw new Error(`Mission with ID ${missionId} not found`);
      }
      return mission;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch mission');
    }
  }
);

export const startMission = createAsyncThunk(
  'mission/startMission',
  async (missionId: string, { rejectWithValue }) => {
    try {
      const progress = await missionService.startMission(missionId);
      return { missionId, progress };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to start mission');
    }
  }
);

export const completeObjective = createAsyncThunk(
  'mission/completeObjective',
  async ({ missionId, objectiveId }: { missionId: string, objectiveId: string }, { rejectWithValue }) => {
    try {
      const progress = await missionService.completeObjective(missionId, objectiveId);
      return { missionId, objectiveId, progress };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to complete objective');
    }
  }
);

export const failObjective = createAsyncThunk(
  'mission/failObjective',
  async ({ missionId, objectiveId, reason }: { missionId: string, objectiveId: string, reason: string }, { rejectWithValue }) => {
    try {
      const progress = await missionService.failObjective(missionId, objectiveId, reason);
      return { missionId, objectiveId, reason, progress };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fail objective');
    }
  }
);

// Mission slice
export const missionSlice = createSlice({
  name: 'mission',
  initialState,
  reducers: {
    setActiveMission: (state, action: PayloadAction<string | null>) => {
      state.activeMissionId = action.payload;
    },
    resetMissionState: (state) => {
      return initialState;
    },
    updateMissionProgress: (state, action: PayloadAction<{ missionId: string, progress: Partial<MissionProgress> }>) => {
      const { missionId, progress } = action.payload;
      
      if (state.missionProgress[missionId]) {
        state.missionProgress[missionId] = {
          ...state.missionProgress[missionId],
          ...progress,
          // Ensure we keep mission ID
          missionId
        };
      }
    }
  },
  extraReducers: (builder) => {
    // Fetch missions
    builder.addCase(fetchMissions.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMissions.fulfilled, (state, action: PayloadAction<Mission[]>) => {
      state.isLoading = false;
      
      // Update missions record
      action.payload.forEach(mission => {
        state.missions[mission.id] = mission;
      });
      
      // Update mission lists by status
      state.availableMissions = action.payload
        .filter(mission => mission.status === 'available')
        .map(mission => mission.id);
      
      state.inProgressMissions = action.payload
        .filter(mission => mission.status === 'in_progress')
        .map(mission => mission.id);
      
      state.completedMissions = action.payload
        .filter(mission => mission.status === 'completed')
        .map(mission => mission.id);
      
      state.failedMissions = action.payload
        .filter(mission => mission.status === 'failed')
        .map(mission => mission.id);
    });
    builder.addCase(fetchMissions.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string || 'Failed to fetch missions';
    });
    
    // Fetch mission by ID
    builder.addCase(fetchMissionById.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchMissionById.fulfilled, (state, action: PayloadAction<Mission>) => {
      state.isLoading = false;
      const mission = action.payload;
      
      // Add or update the mission
      state.missions[mission.id] = mission;
      
      // Ensure it's in the correct list based on status
      state.availableMissions = state.availableMissions.filter(id => id !== mission.id);
      state.inProgressMissions = state.inProgressMissions.filter(id => id !== mission.id);
      state.completedMissions = state.completedMissions.filter(id => id !== mission.id);
      state.failedMissions = state.failedMissions.filter(id => id !== mission.id);
      
      // Add to appropriate list
      switch (mission.status) {
        case 'available':
          state.availableMissions.push(mission.id);
          break;
        case 'in_progress':
          state.inProgressMissions.push(mission.id);
          break;
        case 'completed':
          state.completedMissions.push(mission.id);
          break;
        case 'failed':
          state.failedMissions.push(mission.id);
          break;
      }
    });
    builder.addCase(fetchMissionById.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string || 'Failed to fetch mission';
    });
    
    // Start Mission
    builder.addCase(startMission.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(startMission.fulfilled, (state, action: PayloadAction<MissionStartedPayload>) => {
      state.isLoading = false;
      const { missionId, progress } = action.payload;
      
      // Update mission status
      if (state.missions[missionId]) {
        state.missions[missionId].status = 'in_progress';
        
        // Move mission to in-progress list
        state.availableMissions = state.availableMissions.filter(id => id !== missionId);
        if (!state.inProgressMissions.includes(missionId)) {
          state.inProgressMissions.push(missionId);
        }
      }
      
      // Store mission progress
      state.missionProgress[missionId] = progress;
      
      // Set as active mission
      state.activeMissionId = missionId;
    });
    builder.addCase(startMission.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string || 'Failed to start mission';
    });
    
    // Complete Objective
    builder.addCase(completeObjective.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(completeObjective.fulfilled, (state, action: PayloadAction<ObjectiveCompletedPayload>) => {
      state.isLoading = false;
      const { missionId, objectiveId, progress } = action.payload;
      
      // Update objective status in mission
      if (state.missions[missionId]) {
        const mission = state.missions[missionId];
        state.missions[missionId] = {
          ...mission,
          objectives: mission.objectives.map(obj => 
            obj.id === objectiveId ? { ...obj, status: 'completed' } : obj
          )
        };
      }
      
      // Update mission progress
      state.missionProgress[missionId] = progress;
    });
    builder.addCase(completeObjective.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string || 'Failed to complete objective';
    });
    
    // Fail Objective
    builder.addCase(failObjective.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(failObjective.fulfilled, (state, action: PayloadAction<ObjectiveFailedPayload>) => {
      state.isLoading = false;
      const { missionId, objectiveId, progress } = action.payload;
      
      // Update objective status in mission
      if (state.missions[missionId]) {
        const mission = state.missions[missionId];
        state.missions[missionId] = {
          ...mission,
          objectives: mission.objectives.map(obj => 
            obj.id === objectiveId ? { ...obj, status: 'failed' } : obj
          )
        };
      }
      
      // Update mission progress
      state.missionProgress[missionId] = progress;
    });
    builder.addCase(failObjective.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string || 'Failed to fail objective';
    });
  }
});

export const { setActiveMission, resetMissionState, updateMissionProgress } = missionSlice.actions;

export default missionSlice.reducer;
