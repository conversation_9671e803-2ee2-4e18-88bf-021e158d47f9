import React from 'react';
import TabBar from '../../tab-bar/ui/TabBar';
import TabContent from '../../tab-content/ui/TabContent';
import TopBar from '../../top-bar/ui/TopBar';
import BottomBar from '../../bottom-bar/ui/BottomBar';
import { NotificationList } from '../../notifications';
import styles from './AppShell.module.css';

const AppShell: React.FC = () => {
  return (
    <div className={styles.appShell}>
      {/* Top area with configurable widgets, system info, time, etc. */}
      <TopBar />
      
      {/* Main application area */}
      <div className={styles.mainArea}>
        {/* Active tab content */}
        <div className={styles.contentArea}>
          <TabContent />
        </div>
      </div>
      
      {/* Bottom area with app launching */}
      <BottomBar />
      
      {/* Notifications */}
      <NotificationList />
    </div>
  );
};

export default AppShell;
