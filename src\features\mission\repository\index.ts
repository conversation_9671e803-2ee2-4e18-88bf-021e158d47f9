import { MissionRepository } from '@/core/common/repositories/interfaces';
import {
  Mission,
  MissionProgress,
  MissionStatus,
  ObjectiveStatus,
  MissionType,
  MissionCategory,
  MissionDifficulty
} from '@/core/mission/types';

/**
 * Mock implementation of Mission Repository for local development
 */
export class MockMissionRepository implements MissionRepository {
  // In-memory storage
  private missions: Record<string, Mission> = {};
  private missionProgress: Record<string, MissionProgress> = {};
  
  constructor() {
    // Initialize with some sample missions
    this.initializeSampleMissions();
  }
  
  private initializeSampleMissions() {
    const sampleMissions: Mission[] = [
      {
        id: 'mission-1',
        title: 'Data Breach Investigation',
        description: 'Analyze a compromised system to identify how attackers gained access and what data was stolen.',
        brief: 'A financial services company has detected unusual activity on their network. They suspect a data breach and need your expertise to investigate the attack, identify the entry point, and determine what information was compromised.',
        faction: 'Security Operations Center',
        type: 'forensic',
        category: 'defensive',
        objectives: [
          {
            id: 'obj-1-1',
            title: 'Analyze System Logs',
            description: 'Review system logs to identify suspicious activities and potential entry points.',
            status: 'pending',
            required: true,
            completionSteps: [
              {
                id: 'step-1-1-1',
                description: 'Examine authentication logs for failed login attempts',
                requiresUserAction: true,
                actionType: 'terminal_command',
                actionParams: {
                  expectedCommand: 'cat /var/log/auth.log | grep "Failed password"'
                }
              }
            ]
          },
          {
            id: 'obj-1-2',
            title: 'Identify Malware',
            description: 'Scan the system for malware and identify any malicious software.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-1-1']
          },
          {
            id: 'obj-1-3',
            title: 'Determine Data Exfiltration',
            description: 'Analyze network traffic to determine what data was stolen.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-1-2']
          },
          {
            id: 'obj-1-4',
            title: 'Prepare Incident Report',
            description: 'Document your findings in a comprehensive incident report.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-1-3']
          }
        ],
        rewards: {
          credits: 2500,
          experience: 500,
          reputation: {
            'Security Operations Center': 15
          }
        },
        riskLevel: 3,
        difficulty: 'professional',
        timeConstraints: null,
        requiredSpecializations: ['forensics'],
        requiredResources: [],
        status: 'available',
        location: 'Internal Network',
        targetSystem: 'Financial Services Database'
      },
      {
        id: 'mission-2',
        title: 'Corporate Database Infiltration',
        description: 'Gain access to a corporate database to extract valuable trade secrets.',
        brief: 'A rival corporation has developed a revolutionary new product. Your client wants you to infiltrate their network, bypass security measures, and extract the product specifications from their secure database.',
        faction: 'Crime Syndicate',
        type: 'hack',
        category: 'offensive',
        objectives: [
          {
            id: 'obj-2-1',
            title: 'Reconnaissance',
            description: 'Gather information about the target network and identify potential vulnerabilities.',
            status: 'pending',
            required: true
          },
          {
            id: 'obj-2-2',
            title: 'Gain Initial Access',
            description: 'Exploit a vulnerability to gain initial access to the network.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-2-1']
          },
          {
            id: 'obj-2-3',
            title: 'Escalate Privileges',
            description: 'Escalate your privileges to gain access to restricted systems.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-2-2']
          },
          {
            id: 'obj-2-4',
            title: 'Access Database',
            description: 'Locate and access the database containing the product specifications.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-2-3']
          },
          {
            id: 'obj-2-5',
            title: 'Extract Data',
            description: 'Extract the product specifications without triggering alerts.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-2-4']
          },
          {
            id: 'obj-2-6',
            title: 'Cover Your Tracks',
            description: 'Remove evidence of your presence from the system.',
            status: 'pending',
            required: false,
            dependsOn: ['obj-2-5']
          }
        ],
        rewards: {
          credits: 5000,
          experience: 750,
          reputation: {
            'Crime Syndicate': 20,
            'Security Operations Center': -10
          }
        },
        riskLevel: 7,
        difficulty: 'expert',
        timeConstraints: null,
        requiredSpecializations: ['hacking', 'social_engineering'],
        requiredResources: [],
        status: 'available',
        location: 'Corporate Network',
        targetSystem: 'R&D Database'
      },
      {
        id: 'mission-3',
        title: 'Security Vulnerability Disclosure',
        description: 'Identify and responsibly disclose a security vulnerability in a popular software.',
        brief: 'A widely used open-source software has potential security vulnerabilities. Your task is to analyze the code, identify vulnerabilities, create a proof-of-concept, and responsibly disclose your findings to the developers.',
        faction: 'Independent Researchers',
        type: 'hack',
        category: 'research',
        objectives: [
          {
            id: 'obj-3-1',
            title: 'Code Analysis',
            description: 'Analyze the codebase to identify potential vulnerabilities.',
            status: 'pending',
            required: true
          },
          {
            id: 'obj-3-2',
            title: 'Develop Proof-of-Concept',
            description: 'Create a proof-of-concept that demonstrates the vulnerability.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-3-1']
          },
          {
            id: 'obj-3-3',
            title: 'Document Findings',
            description: 'Prepare detailed documentation of the vulnerability and how it could be exploited.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-3-2']
          },
          {
            id: 'obj-3-4',
            title: 'Responsible Disclosure',
            description: 'Contact the developers and responsibly disclose the vulnerability.',
            status: 'pending',
            required: true,
            dependsOn: ['obj-3-3']
          }
        ],
        rewards: {
          credits: 1500,
          experience: 1000,
          reputation: {
            'Independent Researchers': 25,
            'Security Operations Center': 5
          }
        },
        riskLevel: 2,
        difficulty: 'professional',
        timeConstraints: null,
        requiredSpecializations: ['hacking', 'software_development'],
        requiredResources: [],
        status: 'available',
        location: 'Open Source Repository',
        targetSystem: 'Software Codebase'
      }
    ];
    
    // Add sample missions to storage
    sampleMissions.forEach(mission => {
      this.missions[mission.id] = mission;
    });
  }
  
  // Mission CRUD operations
  async getAvailableMissions(): Promise<Mission[]> {
    return Object.values(this.missions).filter(m => m.status === 'available');
  }
  
  async getMissionById(id: string): Promise<Mission | null> {
    return this.missions[id] || null;
  }
  
  async saveMission(mission: Mission): Promise<void> {
    this.missions[mission.id] = mission;
  }
  
  async updateMissionStatus(id: string, status: MissionStatus): Promise<void> {
    const mission = this.missions[id];
    if (mission) {
      this.missions[id] = { ...mission, status };
    }
  }
  
  async deleteMission(id: string): Promise<void> {
    delete this.missions[id];
  }
  
  // Objective management
  async updateObjectiveStatus(missionId: string, objectiveId: string, status: ObjectiveStatus): Promise<void> {
    const mission = this.missions[missionId];
    if (mission) {
      const objectives = mission.objectives.map(obj => 
        obj.id === objectiveId ? { ...obj, status } : obj
      );
      
      this.missions[missionId] = { ...mission, objectives };
    }
  }
  
  // Progress tracking
  async getMissionProgress(missionId: string): Promise<MissionProgress | null> {
    return this.missionProgress[missionId] || null;
  }
  
  async saveMissionProgress(missionId: string, progress: MissionProgress): Promise<void> {
    this.missionProgress[missionId] = progress;
  }
  
  // Time tracking
  async setMissionStartTime(missionId: string, timestamp: number): Promise<void> {
    const mission = this.missions[missionId];
    if (mission) {
      this.missions[missionId] = { 
        ...mission, 
        startedAt: new Date(timestamp).toISOString() 
      };
    }
  }
  
  async setMissionCompletionTime(missionId: string, timestamp: number): Promise<void> {
    const mission = this.missions[missionId];
    if (mission) {
      this.missions[missionId] = { 
        ...mission, 
        completedAt: new Date(timestamp).toISOString() 
      };
    }
  }
  
  async setMissionFailureTime(missionId: string, timestamp: number): Promise<void> {
    const mission = this.missions[missionId];
    if (mission) {
      this.missions[missionId] = { 
        ...mission, 
        failedAt: new Date(timestamp).toISOString() 
      };
    }
  }
  
  // Mission search and filtering
  async getMissionsByFaction(factionId: string): Promise<Mission[]> {
    return Object.values(this.missions).filter(m => m.faction === factionId);
  }
  
  async getMissionsByStatus(status: MissionStatus): Promise<Mission[]> {
    return Object.values(this.missions).filter(m => m.status === status);
  }
  
  async getMissionsByType(type: string): Promise<Mission[]> {
    return Object.values(this.missions).filter(m => m.type === type);
  }
}
