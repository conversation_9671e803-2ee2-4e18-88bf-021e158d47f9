import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardBody, <PERSON>ge, But<PERSON> } from '@/shared/ui';
import { TrainingProgram } from '@/core/specialist/types';
import styles from './TrainingCenter.module.css';
import {
  IconSchool,
  IconClock,
  IconCoin,
  IconArrowUp,
  IconFilter,
  IconArrowNarrowRight,
  IconDatabaseOff,
  IconSearch,
  IconChevronDown,
  IconUserCheck,
  IconBrain,
  IconCertificate
} from '@tabler/icons-react';
import clsx from 'clsx';

export interface TrainingCenterProps {
  programs: TrainingProgram[];
  onSelectProgram: (programId: string) => void;
  specialistLevel?: number;
  availableCredits?: number;
  loading?: boolean;
  className?: string;
  title?: string;
  selectedSpecialist?: string;
}

const TrainingCenter: React.FC<TrainingCenterProps> = ({
  programs,
  onSelectProgram,
  specialistLevel = 1,
  availableCredits = 0,
  loading = false,
  className,
  title = 'Training Programs',
  selectedSpecialist
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [filter, setFilter] = useState<{ skillType?: string; minLevel?: number; maxCost?: number }>({});
  
  const toggleFilterPanel = () => {
    setShowFilterPanel(!showFilterPanel);
  };
  
  // Format duration to display days/hours
  const formatDuration = (duration: number): string => {
    if (duration < 24) {
      return `${duration} hour${duration !== 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(duration / 24);
      const hours = duration % 24;
      return `${days} day${days !== 1 ? 's' : ''}${hours > 0 ? ` ${hours} hour${hours !== 1 ? 's' : ''}` : ''}`;
    }
  };
  
  // Filter programs based on search query and filters
  const filteredPrograms = programs.filter(program => {
    // Filter by search query
    if (searchQuery && !program.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !program.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Filter by skill type
    if (filter.skillType && filter.skillType !== 'all') {
      const hasSkill = program.targetSkills.some(skill => 
        skill.skill.includes(filter.skillType!)
      );
      if (!hasSkill) return false;
    }
    
    // Filter by required level
    if (filter.minLevel !== undefined && program.requiredLevel && program.requiredLevel < filter.minLevel) {
      return false;
    }
    
    // Filter by cost
    if (filter.maxCost !== undefined && program.cost > filter.maxCost) {
      return false;
    }
    
    return true;
  });
  
  // Format skill name for display
  const formatSkillName = (skill: string): string => {
    return skill.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };
  
  // Check if program is available based on specialist level
  const isProgramAvailable = (program: TrainingProgram): boolean => {
    if (!program.requiredLevel) return true;
    return specialistLevel >= program.requiredLevel;
  };
  
  // Check if program is affordable
  const isProgramAffordable = (program: TrainingProgram): boolean => {
    return program.cost <= availableCredits;
  };
  
  // Group target skills by category
  const getSkillCategories = (program: TrainingProgram): Record<string, { skill: string; improvement: number }[]> => {
    const categories: Record<string, { skill: string; improvement: number }[]> = {};
    
    program.targetSkills.forEach(({ skill, improvement }) => {
      let category = 'other';
      
      if (skill.includes('hacking') || skill.includes('crypto') || skill.includes('malware') || 
          skill.includes('network') || skill.includes('exploitation')) {
        category = 'technical';
      } else if (skill.includes('social') || skill.includes('persuasion') || skill.includes('deception')) {
        category = 'social';
      } else if (skill.includes('intel') || skill.includes('osint') || skill.includes('threat')) {
        category = 'intelligence';
      } else if (skill.includes('physical') || skill.includes('surveillance') || skill.includes('counter')) {
        category = 'physical';
      }
      
      if (!categories[category]) {
        categories[category] = [];
      }
      
      categories[category].push({ skill, improvement });
    });
    
    return categories;
  };
  
  // Get category title
  const getCategoryTitle = (category: string): string => {
    switch (category) {
      case 'technical': return 'Technical Skills';
      case 'social': return 'Social Skills';
      case 'intelligence': return 'Intelligence Skills';
      case 'physical': return 'Physical Skills';
      default: return 'Other Skills';
    }
  };
  
  if (loading) {
    return (
      <Card className={clsx(styles.trainingCenter, className)} variant="default">
        <CardHeader title={title} />
        <CardBody>
          <div className={styles.loadingContainer}>
            <div className={styles.loadingSpinner}>
              <IconSchool size={32} className={styles.loadingIcon} stroke={1.5} />
            </div>
            <p className={styles.loadingText}>Loading training programs...</p>
          </div>
        </CardBody>
      </Card>
    );
  }
  
  return (
    <Card className={clsx(styles.trainingCenter, className)} variant="default">
      <CardHeader 
        title={
          <div className={styles.headerTitle}>
            <IconSchool size={18} className={styles.headerIcon} stroke={1.5} />
            {title}
          </div>
        }
        subtitle={
          selectedSpecialist ? 
            <div className={styles.headerSubtitle}>
              <Badge colorScheme="info" variant="subtle" size="sm">
                <IconUserCheck size={14} className={styles.badgeIcon} stroke={1.5} />
                Specialist Level: {specialistLevel}
              </Badge>
              <Badge colorScheme="success" variant="subtle" size="sm">
                <IconCoin size={14} className={styles.badgeIcon} stroke={1.5} />
                Available Credits: {availableCredits}
              </Badge>
            </div> : undefined
        }
      />
      <CardBody>
        <div className={styles.searchContainer}>
          <div className={styles.searchWrapper}>
            <IconSearch size={18} className={styles.searchIcon} stroke={1.5} />
            <input 
              type="text" 
              placeholder="Search programs..." 
              className={styles.searchInput}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <Button 
            variant="secondary" 
            size="sm"
            leftIcon={<IconFilter size={16} stroke={1.5} />}
            rightIcon={<IconChevronDown size={14} stroke={1.5} className={clsx(styles.dropdownIcon, { [styles.dropdownIconOpen]: showFilterPanel })} />}
            onClick={toggleFilterPanel}
            className={styles.filterButton}
          >
            Filter
          </Button>
        </div>
        
        {showFilterPanel && (
          <div className={styles.filterPanel}>
            <div className={styles.filterSection}>
              <h3 className={styles.filterTitle}>Skill Category</h3>
              <div className={styles.filterOptions}>
                {['all', 'technical', 'social', 'intelligence', 'physical'].map(category => (
                  <div 
                    key={category} 
                    className={clsx(styles.filterOption, {
                      [styles.filterOptionActive]: filter.skillType === category
                    })}
                    onClick={() => setFilter({ ...filter, skillType: category })}
                  >
                    {getCategoryTitle(category)}
                  </div>
                ))}
              </div>
            </div>
            
            <div className={styles.filterSection}>
              <h3 className={styles.filterTitle}>Maximum Cost</h3>
              <div className={styles.filterOptions}>
                {[1000, 3000, 5000, 10000, 'any'].map(cost => (
                  <div 
                    key={typeof cost === 'number' ? cost.toString() : cost} 
                    className={clsx(styles.filterOption, {
                      [styles.filterOptionActive]: filter.maxCost === (cost === 'any' ? undefined : cost)
                    })}
                    onClick={() => setFilter({ ...filter, maxCost: cost === 'any' ? undefined : Number(cost) })}
                  >
                    {cost === 'any' ? 'Any Cost' : `${cost.toLocaleString()} cr`}
                  </div>
                ))}
              </div>
            </div>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setFilter({})}
              className={styles.resetFilterButton}
            >
              Reset Filters
            </Button>
          </div>
        )}
        
        {filteredPrograms.length === 0 ? (
          <div className={styles.emptyState}>
            <IconDatabaseOff size={48} className={styles.emptyIcon} stroke={1.5} />
            <p className={styles.emptyText}>No training programs match your filters</p>
            <Button 
              variant="secondary" 
              size="sm"
              onClick={() => {
                setSearchQuery('');
                setFilter({});
              }}
            >
              Reset Filters
            </Button>
          </div>
        ) : (
          <div className={styles.programsList}>
            {filteredPrograms.map(program => {
              const isAvailable = isProgramAvailable(program);
              const isAffordable = isProgramAffordable(program);
              const canEnroll = isAvailable && isAffordable;
              const skillCategories = getSkillCategories(program);
              
              return (
                <Card 
                  key={program.id} 
                  className={clsx(styles.programCard, {
                    [styles.unavailableProgram]: !isAvailable,
                    [styles.unaffordableProgram]: !isAffordable && isAvailable
                  })}
                  variant="default"
                >
                  <CardHeader 
                    title={
                      <div className={styles.programTitle}>
                        <IconCertificate size={18} className={styles.programIcon} stroke={1.5} />
                        <span>{program.name}</span>
                        {program.requiredLevel && (
                          <Badge 
                            colorScheme={isAvailable ? 'info' : 'danger'} 
                            variant="subtle" 
                            size="sm"
                            className={styles.levelBadge}
                          >
                            <IconBrain size={14} className={styles.badgeIcon} stroke={1.5} />
                            Level {program.requiredLevel}+
                          </Badge>
                        )}
                      </div>
                    }
                    subtitle={
                      <div className={styles.programStats}>
                        <span className={styles.programStat}>
                          <IconClock size={16} className={styles.statIcon} stroke={1.5} />
                          {formatDuration(program.duration)}
                        </span>
                        <span className={clsx(styles.programStat, {
                          [styles.affordableCost]: isAffordable,
                          [styles.unaffordableCost]: !isAffordable
                        })}>
                          <IconCoin size={16} className={styles.statIcon} stroke={1.5} />
                          {program.cost.toLocaleString()} credits
                        </span>
                      </div>
                    }
                  />
                  <CardBody>
                    <p className={styles.programDescription}>{program.description}</p>
                    
                    <div className={styles.skillImprovements}>
                      <h3 className={styles.improvementsTitle}>Skill Improvements</h3>
                      {Object.entries(skillCategories).map(([category, skills]) => (
                        <div key={category} className={styles.skillCategory}>
                          <h4 className={styles.categoryTitle}>{getCategoryTitle(category)}</h4>
                          <div className={styles.skillsList}>
                            {skills.map(({ skill, improvement }) => (
                              <div key={skill} className={styles.skillItem}>
                                <span className={styles.skillName}>{formatSkillName(skill)}</span>
                                <span className={styles.skillImprovement}>
                                  <IconArrowUp size={14} className={styles.improvementIcon} stroke={1.5} />
                                  +{improvement}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <div className={styles.enrollButtonContainer}>
                      <Button
                        variant={canEnroll ? 'primary' : 'secondary'}
                        rightIcon={<IconArrowNarrowRight size={16} stroke={1.5} />}
                        onClick={() => canEnroll && onSelectProgram(program.id)}
                        disabled={!canEnroll}
                        className={styles.enrollButton}
                      >
                        {canEnroll 
                          ? 'Enroll in Program' 
                          : !isAvailable 
                            ? `Requires Level ${program.requiredLevel}` 
                            : 'Insufficient Credits'}
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              );
            })}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default TrainingCenter;