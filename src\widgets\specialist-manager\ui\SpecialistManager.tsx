import React from 'react';
import { SpecialistList } from '../../../features/specialist/ui/specialist-list';
import { SpecialistDetail } from '../../../features/specialist/ui/specialist-detail';

interface SpecialistManagerProps {
  className?: string;
}

const SpecialistManager: React.FC<SpecialistManagerProps> = ({ className }) => {
  const [selectedSpecialistId, setSelectedSpecialistId] = React.useState<string | null>(null);

  return (
    <div className={`specialist-manager ${className || ''}`}>
      <div className="specialist-manager__header">
        <h1>Team Management</h1>
      </div>
      
      <div className="specialist-manager__content">
        <div className="specialist-manager__list">
          <SpecialistList 
            onSelectSpecialist={(specialistId) => setSelectedSpecialistId(specialistId)}
            selectedSpecialistId={selectedSpecialistId}
          />
        </div>
        
        {selectedSpecialistId && (
          <div className="specialist-manager__detail">
            <SpecialistDetail specialistId={selectedSpecialistId} />
          </div>
        )}
      </div>
    </div>
  );
};

export default SpecialistManager;