import React, { useState, useEffect } from 'react';
import { useMissionStore, useMissionSelectors } from '../../../features/mission/model/store';
import { useNotificationsStore } from '../../../features/notifications/model/store';
import MissionList from '../../../widgets/mission-list/ui/MissionList';
import MissionDetail from '../../../widgets/mission-detail/ui/MissionDetail';
import { RewardDisplay } from '../../../widgets/reward-display';
import styles from './MissionPage.module.css';

interface MissionPageProps {
  tabId: string;
}

interface MissionReward {
  type: 'credits' | 'experience' | 'reputation' | 'item' | 'resource';
  name: string;
  description: string;
  value: number | Record<string, number>;
  icon?: string;
}

const MissionPage: React.FC<MissionPageProps> = ({ tabId }) => {
  // Mission store actions
  const { fetchMissions, setActiveMission, startMission, completeObjective } = useMissionStore();

  // Mission data from store
  const {
    availableMissions,
    inProgressMissions,
    isLoading,
    activeMission,
    activeMissionProgress
  } = useMissionSelectors();

  // Notifications store
  const addNotification = useNotificationsStore(state => state.addNotification);

  // View states: 'list' or 'detail'
  const [view, setView] = useState<'list' | 'detail'>('list');
  // Filter state: 'available' or 'in_progress'
  const [filter, setFilter] = useState<'available' | 'in_progress'>('available');
  // Reward display state
  const [showRewards, setShowRewards] = useState(false);
  const [missionRewards, setMissionRewards] = useState<MissionReward[]>([]);
  const [completedMissionTitle, setCompletedMissionTitle] = useState('');

  // Fetch missions on component mount
  useEffect(() => {
    fetchMissions();
  }, [fetchMissions]);
  
  // Handle mission selection
  const handleMissionSelect = (missionId: string) => {
    setActiveMission(missionId);
    setView('detail');
  };

  // Handle going back to mission list
  const handleBackToList = () => {
    setView('list');
  };

  // Handle starting a mission
  const handleStartMission = async () => {
    if (activeMission) {
      try {
        await startMission(activeMission.id);
        // Show success notification
        addNotification({
          type: 'success',
          title: 'Mission Started',
          message: `You have started the mission: ${activeMission.title}`,
        });
      } catch (error) {
        // Show error notification
        addNotification({
          type: 'error',
          title: 'Failed to Start Mission',
          message: error instanceof Error ? error.message : 'An unknown error occurred',
        });
      }
    }
  };
  
  // Convert mission rewards to display format
  const formatRewards = (mission) => {
    const rewards: MissionReward[] = [];
    
    if (mission.rewards.credits) {
      rewards.push({
        type: 'credits',
        name: 'Payment',
        description: 'Credits transferred to your account',
        value: mission.rewards.credits
      });
    }
    
    if (mission.rewards.experience) {
      rewards.push({
        type: 'experience',
        name: 'Experience',
        description: 'Knowledge and skills gained',
        value: mission.rewards.experience
      });
    }
    
    if (mission.rewards.reputation) {
      rewards.push({
        type: 'reputation',
        name: 'Faction Standing',
        description: 'Changes to your reputation',
        value: mission.rewards.reputation
      });
    }
    
    if (mission.rewards.items && mission.rewards.items.length > 0) {
      mission.rewards.items.forEach(item => {
        rewards.push({
          type: 'item',
          name: item.name,
          description: item.description || 'Acquired item',
          value: 1,
          icon: item.icon
        });
      });
    }
    
    if (mission.rewards.resources && mission.rewards.resources.length > 0) {
      mission.rewards.resources.forEach(resource => {
        rewards.push({
          type: 'resource',
          name: resource.name,
          description: resource.description || 'Acquired resource',
          value: resource.quantity || 1,
          icon: resource.icon
        });
      });
    }
    
    return rewards;
  };
  
  // Show rewards display
  const showRewardsDisplay = (mission) => {
    setMissionRewards(formatRewards(mission));
    setCompletedMissionTitle(mission.title);
    setShowRewards(true);
  };
  
  // Handle completing an objective
  const handleCompleteObjective = async (objectiveId: string) => {
    if (activeMission) {
      // Find objective details for notification
      const objective = activeMission.objectives.find(obj => obj.id === objectiveId);

      try {
        await completeObjective(activeMission.id, objectiveId);

        // Show success notification
        addNotification({
          type: 'success',
          title: 'Objective Completed',
          message: objective ? `You completed: ${objective.title}` : 'Objective completed successfully',
        });

        // Check if all required objectives are completed
        const allRequiredCompleted = activeMission.objectives
          .filter(obj => obj.required)
          .every(obj =>
            obj.id === objectiveId ||
            activeMissionProgress?.completedObjectives.includes(obj.id) ||
            obj.status === 'completed'
          );

        // If all required objectives are completed, show mission complete notification and rewards
        if (allRequiredCompleted) {
          addNotification({
            type: 'success',
            title: 'Mission Complete',
            message: `You have successfully completed the mission: ${activeMission.title}`,
            autoDismiss: true,
            actions: [
              {
                label: 'View Rewards',
                action: 'view_rewards'
              }
            ]
          });

          // Show rewards after a short delay
          setTimeout(() => {
            showRewardsDisplay(activeMission);
          }, 1000);
        }
      } catch (error) {
        // Show error notification
        addNotification({
          type: 'error',
          title: 'Failed to Complete Objective',
          message: error instanceof Error ? error.message : 'An unknown error occurred',
        });
      }
    }
  };
  
  return (
    <div className={styles.missionPage}>
      <div className={styles.header}>
        <h1 className={styles.title}>Mission Control</h1>
        {view === 'list' && (
          <div className={styles.filters}>
            <button 
              className={`${styles.filterButton} ${filter === 'available' ? styles.active : ''}`}
              onClick={() => setFilter('available')}
            >
              Available Missions
            </button>
            <button 
              className={`${styles.filterButton} ${filter === 'in_progress' ? styles.active : ''}`}
              onClick={() => setFilter('in_progress')}
            >
              In Progress
            </button>
          </div>
        )}
      </div>
      
      <div className={styles.content}>
        {view === 'list' ? (
          <div className={styles.listContainer}>
            {filter === 'available' ? (
              <MissionList 
                missions={availableMissions}
                onMissionSelect={handleMissionSelect}
                loading={isLoading}
                emptyMessage="No available missions found. Check back later for new contracts."
              />
            ) : (
              <MissionList 
                missions={inProgressMissions}
                onMissionSelect={handleMissionSelect}
                loading={isLoading}
                emptyMessage="You don't have any missions in progress."
              />
            )}
          </div>
        ) : (
          <div className={styles.detailContainer}>
            {activeMission ? (
              <MissionDetail 
                mission={activeMission}
                progress={activeMissionProgress}
                onStartMission={handleStartMission}
                onCompleteObjective={handleCompleteObjective}
                onBack={handleBackToList}
              />
            ) : (
              <div className={styles.noMissionSelected}>
                <p>No mission selected. Please select a mission from the list.</p>
                <button onClick={handleBackToList}>Back to Mission List</button>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Reward display */}
      {showRewards && (
        <RewardDisplay 
          missionTitle={completedMissionTitle}
          rewards={missionRewards}
          onClose={() => {
            setShowRewards(false);
            handleBackToList();
            // Refetch missions to update statuses
            fetchMissions();
          }}
        />
      )}
    </div>
  );
};

export default MissionPage;
