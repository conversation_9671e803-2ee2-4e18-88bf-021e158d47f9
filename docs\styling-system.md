# CyberClash Styling System

## Overview

The CyberClash UI follows a cyberpunk-inspired aesthetic that balances visual appeal with functional clarity. This document presents our comprehensive styling approach, architecture, and guidelines in one place.

## Design Philosophy

- **Cybersecurity Aesthetic**: Dark backgrounds with neon accents, technical feel, atmospheric immersion
- **Functional Clarity**: Information remains accessible despite stylistic choices
- **Consistent Patterns**: Common interaction patterns throughout the application
- **Responsive Design**: Mobile-first approach that adapts to different screen sizes

## CSS Architecture

### Directory Structure

```
src/
├── app/
│   └── styles/            # Global styles
│       ├── base/          # Base foundation styles
│       │   ├── reset.css  # CSS reset
│       │   ├── variables.css  # Design tokens
│       │   ├── typography.css # Font styles
│       │   └── base.css   # Basic element styling
│       ├── effects/       # Visual effects
│       │   ├── glitch.css # Glitch animations
│       │   ├── glow.css   # Neon glow effects
│       │   └── scanlines.css # Screen effects
│       ├── layout/        # Layout components
│       │   ├── grid.css   # Grid system
│       │   ├── containers.css # Container styles
│       │   └── scrollbars.css # Custom scrollbars
│       ├── components/    # Basic UI components
│       │   ├── buttons.css # Button styles
│       │   ├── forms.css  # Input styles
│       │   └── cards.css  # Card styles
│       ├── animations.css # Global animations
│       ├── utilities.css  # Utility classes
│       └── index.css      # Main import file
└── widgets/
    └── [Widget]/
        └── styles.css     # Component-specific styles
```

### Organization Principles

1. **Separation of Concerns**:
   - Global theming in `app/styles`
   - Component-specific styling in component directories 
   - Feature-specific styling isolated to features

2. **Modularity and Reuse**:
   - Common elements defined once
   - Components built from shared base styles
   - Clear inheritance hierarchy

3. **Performance Considerations**:
   - Optimized animations
   - Minimal CSS-in-JS usage
   - Efficient selectors

## Color System

### Base Colors

- **Background**: 
  - Primary: #1A1A1A (Deep dark)
  - Secondary: #20223C (Dark blue-gray)
  - Tertiary: #2A2A2E (Medium dark)

- **Text**: 
  - Primary: #E0E0E0 (Light gray)
  - Secondary: #B0B0B0 (Medium gray)
  - Disabled: #707070 (Dark gray)

- **Neon Accents**:
  - Primary: #00F0FF (Cyan) - Main interactive elements
  - Secondary: #FF00E4 (Magenta) - Alerts and important information
  - Tertiary: #FFF500 (Yellow) - Warnings and highlights
  - Quaternary: #00FF66 (Green) - Success indicators

### Status Colors

- **Normal**: #007AFF
- **Warning**: #FF9500
- **Critical**: #FF2D55
- **Secure**: #34C759
- **Unknown**: #AF52DE

## Typography

### Font Families

- **Main Text**: 'JetBrains Mono' - Technical, code-like feel
- **Headers**: 'Orbitron' or 'Rajdhani' - Futuristic feel
- **Data Displays**: 'Share Tech Mono' - Readouts, terminal displays

```css
--font-main: 'JetBrains Mono', 'Courier New', monospace;
--font-headers: 'Orbitron', 'Rajdhani', sans-serif;
--font-data: 'Share Tech Mono', monospace;
```

### Font Sizes

- **xs**: 10px - Minimum size for UI elements
- **sm**: 12px - Secondary information, labels
- **md**: 14px - Body text, form inputs
- **lg**: 16px - Primary UI text
- **xl**: 18px - Section headings
- **xxl**: 24px - Page titles

## Component Styling

### Cards

- Dark, semi-transparent backgrounds
- Thin, bright border accents
- Subtle tech-pattern backgrounds
- Angular corners (slight angling or cut corners)

### Buttons and Controls

- **Primary**: Solid background (#00F0FF) with dark text
- **Secondary**: Dark background with light border
- **Danger**: Red background (#FF3B30) for destructive actions
- **Ghost**: No background/border, just text and hover effect

### Forms

- Clear labels above or to the left
- Inline validation errors
- Consistent focus states
- Logical grouping of related inputs

### Tables

- Zebra striping for readability
- Sticky headers when scrolling
- Consistent cell padding (8px vertical, 16px horizontal)
- Column alignment based on content type

## Visual Effects

### Glow Effects

- Text and border glow in neon colors
- Pulsing glow for active/important elements
- Gradient glow transitions

### Animations

- Digital glitch effects for errors
- Smooth, tech-inspired transitions
- Scanner/radar animations for loading states
- Typed text animation for terminal output

### Tech Patterns

- Subtle grid lines and circuitry patterns
- Low-opacity hexagon/tech patterns in backgrounds
- Digital noise texture in dark areas
- Data flow animations for active processes

## Animation Guidelines

### Timing

- **Fast**: 0.15s - Small element changes
- **Medium**: 0.3s - Panel transitions, tab changes
- **Slow**: 0.5s - Full page transitions

### Easing

- Use ease-in-out for most transitions
- Use ease-out for expanding elements
- Use ease-in for collapsing elements

## CSS Naming Conventions

We follow a modified BEM (Block Element Modifier) approach:

- **Blocks**: Main components (`.terminal`, `.card`, `.mission-card`)
- **Elements**: Component parts (`.terminal__input`, `.card__header`)
- **Modifiers**: Variations (`.button--primary`, `.card--highlighted`)
- **States**: Component states (`.is-active`, `.has-error`)

## Responsive Design

The interface adapts to different screen sizes with:

- Mobile-first approach
- Major breakpoints:
  - Small: 0-640px
  - Medium: 641-1024px
  - Large: 1025px+
- Responsive grid system
- Adaptive typography
- Simplified layouts on smaller screens

## Accessibility Considerations

Despite the stylistic choices, ensure:

- Sufficient color contrast for text readability (minimum 4.5:1 ratio)
- Keyboard navigation highlighting
- Alternative visual cues beyond color
- Scalable text and responsive layouts
- Reduced motion alternatives

## Implementation Examples

Refer to these files for well-implemented examples:

- `src/widgets/mission-card/ui/MissionCard.tsx` - Card component
- `src/widgets/tab-bar/ui/TabBar.tsx` - Navigation component
- `src/features/terminal/ui/TerminalConsole.tsx` - Interactive component
