# Core Layer

This layer contains pure business logic with no UI or state management dependencies.

The Core layer is:
- Framework agnostic (no React/Redux dependencies)
- Focused on game rules and logic
- Designed for portability and testability
- Organized by domain

Each domain contains:
- Interfaces: Contracts for services and repositories
- Services: Implementation of game logic
- Types: Domain-specific types and models

This architecture allows us to:
- Test game logic independent of UI
- Migrate to a backend with minimal changes
- Maintain clear separation of concerns
