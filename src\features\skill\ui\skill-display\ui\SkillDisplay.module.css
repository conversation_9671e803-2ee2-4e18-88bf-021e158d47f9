/* SkillDisplay styles */

.skillDisplay {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Category styling */
.skillCategory {
  margin-bottom: var(--space-md);
}

.skillCategory:last-child {
  margin-bottom: 0;
}

.categoryTitle {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-text-secondary);
  margin: 0 0 var(--space-sm);
  padding-bottom: var(--space-xs);
  border-bottom: 1px solid var(--color-border-primary);
  font-family: var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Skill list styling */
.skillList {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.skillItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skillHeader {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.skillIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--color-accent-primary);
}

.skillName {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  font-family: var(--font-mono);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.skillValue {
  font-family: var(--font-mono);
  font-weight: 600;
  font-size: var(--font-size-sm);
  color: var(--color-accent-primary);
  min-width: 30px;
  text-align: right;
}

.skillBarContainer {
  height: 6px;
  background-color: var(--color-bg-tertiary);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.skillBar {
  height: 100%;
  border-radius: 3px;
  position: relative;
  transition: width 0.5s ease-out;
}

/* Skill bar level gradients */
.expertLevel {
  background: linear-gradient(90deg, #00F0FF, #00B8FF);
  box-shadow: 0 0 8px rgba(0, 240, 255, 0.5);
}

.advancedLevel {
  background: linear-gradient(90deg, #00B8FF, #0090FF);
  box-shadow: 0 0 6px rgba(0, 184, 255, 0.4);
}

.intermediateLevel {
  background: linear-gradient(90deg, #0090FF, #007AFF);
  box-shadow: 0 0 4px rgba(0, 144, 255, 0.3);
}

.basicLevel {
  background: linear-gradient(90deg, #007AFF, #0055FF);
  box-shadow: 0 0 2px rgba(0, 122, 255, 0.2);
}

.noviceLevel {
  background: linear-gradient(90deg, #0055FF, #0033FF);
  box-shadow: 0 0 2px rgba(0, 85, 255, 0.1);
}

/* Shimmer effect */
.skillBar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Compact version styling */
.skillDisplayCompact {
  width: 100%;
}

.skillListCompact {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.skillItemCompact {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-bg-tertiary);
  transition: transform 0.2s ease;
}

.skillItemCompact:hover {
  transform: translateX(4px);
}

.skillIconCompact {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  color: var(--color-accent-primary);
}

.skillInfoCompact {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0; /* Allows text-overflow to work */
}

.skillNameCompact {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.skillBarContainerCompact {
  height: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.skillBarCompact {
  height: 100%;
  border-radius: 2px;
  transition: width 0.5s ease-out;
}

.skillValueCompact {
  font-family: var(--font-mono);
  font-weight: 600;
  font-size: var(--font-size-xs);
  color: var(--color-accent-primary);
  min-width: 24px;
  text-align: right;
}

/* Empty state */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
  text-align: center;
}

.emptyIcon {
  color: var(--color-text-disabled);
  margin-bottom: var(--space-md);
  opacity: 0.7;
}

.emptyText {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
  font-family: var(--font-mono);
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .skillItem {
    padding: var(--space-xs);
  }
  
  .skillIcon, .skillIconCompact {
    width: 20px;
    height: 20px;
  }
}