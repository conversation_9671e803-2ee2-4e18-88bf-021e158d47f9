// Common ID types
export type FactionId = string;
export type MissionId = string;
export type SpecializationId = string;
export type SkillId = string;
export type ResourceId = string;
export type ContactId = string;
export type ToolId = string;
export type VendorId = string;
export type ItemId = string;
export type MarketId = string;
export type TabId = string;

// Tab-related types (for future use)
export interface Tab {
  id: TabId;
  title: string;
  icon?: string;
  closable: boolean;
  type: TabType;
  data?: Record<string, unknown>;
}

export type TabType =
  | 'terminal'
  | 'mission'
  | 'market'
  | 'team'
  | 'intel'
  | 'messaging'
  | 'settings'
  | 'dashboard';

// Layout-related types
export type LayoutType =
  | 'single'     // One tab taking full space
  | 'split'      // Two tabs side by side
  | 'triple'     // Main tab (60%) with two stacked tabs (40%)
  | 'grid';      // 2x2 grid layout
