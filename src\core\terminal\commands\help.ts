import { Command, CommandResult, CommandContext } from '../types';

class HelpCommand implements Command {
  private commands: Record<string, Command> = {};
  
  help = 'Displays help information about available commands.\nUsage: help [command]';
  
  setCommands(commands: Record<string, Command>) {
    this.commands = commands;
  }
  
  execute(args: string[], context: CommandContext): CommandResult {
    let output = 'Available commands:\n\n';
    
    // Get all commands or specific command help
    if (args.length > 0 && this.commands[args[0]]) {
      output = `Help for '${args[0]}':\n${this.commands[args[0]].help}`;
    } else {
      // List all commands with brief descriptions
      Object.entries(this.commands).forEach(([cmd, handler]) => {
        const helpLines = handler.help.split('\n');
        const briefHelp = helpLines[0]; // First line is brief description
        output += `${cmd.padEnd(15)} - ${briefHelp}\n`;
      });
      
      output += '\nFor more information on a specific command, type: help [command]';
    }
    
    return { output, status: 'success' };
  }
}

export const helpCommand = new HelpCommand();
