/* Tab Bar Styles */

.tabBar {
  display: flex;
  align-items: center;
  height: var(--tab-height);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--space-sm);
  overflow-x: auto;
  scrollbar-width: thin;
  position: relative;
}

/* Add a subtle glow to the bottom of the tab bar */
.tabBar::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-active);
  box-shadow: 0 0 10px 1px var(--neon-cyan);
  opacity: 0.5;
}

.tab {
  display: flex;
  align-items: center;
  height: calc(var(--tab-height) - 4px);
  padding: 0 var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  background-color: transparent;
  border: none;
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  margin-right: 2px;
  user-select: none;
  white-space: nowrap;
}

.tab:hover {
  color: var(--text-primary);
  background-color: rgba(0, 240, 255, 0.05);
}

.tabActive {
  color: var(--text-accent);
  background-color: rgba(0, 240, 255, 0.1);
  border-bottom: 2px solid var(--neon-cyan);
}

.tabActive::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--neon-cyan);
  box-shadow: 0 0 10px 1px var(--neon-cyan);
}

.tabIcon {
  margin-right: var(--space-xs);
  font-size: 14px;
  opacity: 0.8;
}

.tabText {
  font-family: var(--font-mono);
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.tabCloseButton {
  margin-left: var(--space-xs);
  opacity: 0.5;
  font-size: 12px;
  transition: opacity var(--transition-fast);
}

.tabCloseButton:hover {
  opacity: 1;
  color: var(--neon-pink);
}

.addTab {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-left: var(--space-sm);
  font-size: 16px;
  color: var(--text-secondary);
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.addTab:hover {
  color: var(--text-accent);
  border-color: var(--border-active);
  box-shadow: 0 0 5px rgba(0, 240, 255, 0.3);
}

/* Circuit pattern on hover */
.tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 5 L 15 5 M 15 5 L 15 15 M 15 15 L 5 15 M 5 15 L 5 5' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1' fill='none'/%3E%3C/svg%3E");
  opacity: 0;
  transition: opacity var(--transition-fast);
  pointer-events: none;
  z-index: -1;
}

.tab:hover::before,
.tabActive::before {
  opacity: 1;
}
