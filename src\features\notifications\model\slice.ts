import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { NotificationsState, Notification, AddNotificationPayload, RemoveNotificationPayload } from './types';

const initialState: NotificationsState = {
  notifications: []
};

export const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<AddNotificationPayload>) => {
      const { type, title, message, autoDismiss = true, dismissAfter = 5000, actions } = action.payload;
      
      const notification: Notification = {
        id: `notif-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        type,
        title,
        message,
        timestamp: Date.now(),
        autoDismiss,
        dismissAfter,
        actions
      };
      
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<RemoveNotificationPayload>) => {
      const { id } = action.payload;
      state.notifications = state.notifications.filter(notif => notif.id !== id);
    },
    clearAllNotifications: (state) => {
      state.notifications = [];
    }
  }
});

export const { addNotification, removeNotification, clearAllNotifications } = notificationsSlice.actions;

export default notificationsSlice.reducer;
