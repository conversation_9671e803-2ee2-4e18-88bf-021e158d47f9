// Core domain types for Terminal functionality
export interface CommandContext {
  currentDirectory: string;
  environmentVariables?: Record<string, string>;
  userId?: string;
}

export interface CommandResult {
  output: string;
  status: 'success' | 'error';
  newDirectory?: string; // For cd command or similar that changes state
}

export interface Command {
  execute: (args: string[], context: CommandContext) => CommandResult;
  help: string;
}

// File system related types
export type FileType = 'file' | 'directory';

export interface FileSystemEntry {
  name: string;
  type: FileType;
  content?: string; // For files
  children?: string[]; // For directories, contains paths to child entries
  metadata?: {
    createdAt: string;
    modifiedAt: string;
    permissions: string;
    owner: string;
    size?: number;
  };
}

export interface FileSystemState {
  entries: Record<string, FileSystemEntry>;
  rootDirectory: string;
}
