# CyberClash UI System

## Overview

This document outlines the design principles, component patterns, and interaction guidelines for the CyberClash user interface.

## Design Philosophy

- **Cybersecurity Aesthetic**: Dark backgrounds with neon accents, technical feel, atmospheric immersion
- **Functional Clarity**: Information remains accessible despite stylistic choices
- **Consistent Patterns**: Common interaction patterns throughout the application
- **Responsive Design**: Mobile-first approach that adapts to different screen sizes

## Application Structure

### AppShell

The main application container with the following structure:

```
┌─────────────────────────────────────┐
│               TopBar                 │ - Configurable widgets, notifications
├─────────────────────────────────────┤
│                                       │
│                                       │
│            Content Area              │ - Main app content with tabs
│             (TabContent)              │
│                                       │
│                                       │
├─────────────────────────────────────┤
│              BottomBar               │ - App launcher with active/inactive apps
└─────────────────────────────────────┘
```

### TopBar

- Height: `--header-height` (48px)
- Contains three main sections: brand, widgets area, and action buttons
- **Configurable Widgets**: Users can select which information widgets to display
- **Widget Types**: Time, Date, Security Level, Credits, etc.
- **Notifications**: Icon with unread indicator in the action area
- **Settings**: Button to configure visible widgets

### TabBar & Content

- TabBar is displayed at the top of the Content Area
- Contains tabs for all active applications (maximum 4)
- Tab elements include icon, title, and close button
- Content Layout adapts based on number of open tabs:
  - **Single**: Full screen for one tab
  - **Split**: 50/50 horizontal split for two tabs
  - **Triple**: 60/40 split with stacked panes for three tabs
  - **Grid**: 2x2 grid for four tabs

### BottomBar

- Height: `--footer-height` (56px)
- **Purpose**: App launcher with active/inactive state indicators
- **Important**: No system information here - exclusively for app navigation
- Shows all available applications as clickable buttons
- Active apps highlighted with glow effects and color changes
- Maximum 4 applications can be active simultaneously

## Component Styling

### Colors

- **Background**: 
  - Primary: #1A1A1A (Deep dark)
  - Secondary: #20223C (Dark blue-gray)
  - Tertiary: #2A2A2E (Medium dark)

- **Text**: 
  - Primary: #E0E0E0 (Light gray)
  - Secondary: #B0B0B0 (Medium gray)
  - Disabled: #707070 (Dark gray)

- **Neon Accents**:
  - Primary: #00F0FF (Cyan) - Main interactive elements
  - Secondary: #FF00E4 (Magenta) - Alerts and important information
  - Tertiary: #FFF500 (Yellow) - Warnings and highlights
  - Quaternary: #00FF66 (Green) - Success indicators

### Typography

- **Main Text**: 'JetBrains Mono' - Technical, code-like feel
- **Headers**: 'Orbitron' or 'Rajdhani' - Futuristic feel
- **Data Displays**: 'Share Tech Mono' - Readouts, terminal displays

### Interactive States

- **Default**: Neutral coloring with subtle borders
- **Hover**: Slightly brightened with increased opacity
- **Active/Selected**: Neon glow effects with accent colors
- **Disabled**: Reduced opacity with muted colors

## Navigation Patterns

### App Launching

Apps can be launched in two ways:

1. Click an app icon in the BottomBar to open/focus that application
2. Use the add tab (+) button in the TabBar to select from available apps

### Tab Management

- Click a tab to make it active
- Click the close button (×) to close a tab
- Maximum 4 tabs can be open simultaneously 
- When attempting to open more than 4 tabs, user receives a notification

## Using Tabler Icons

The UI uses [Tabler Icons](https://tabler-icons.io/) for a consistent icon set:

- Import from `@tabler/icons-react`
- Standard size should be 20px for most UI elements (18px for tabs, 16px for buttons)
- Maintain consistent stroke width (default: 1.5)
- Match icon color to the surrounding text

## Visual Effects

### Cyberpunk Elements

- **Glow Effects**: Text and borders in neon colors
- **Scan Lines**: Subtle CRT-like scan lines over content
- **Tech Patterns**: Low-opacity circuitry or grid patterns
- **Data Flow**: Animated gradients to suggest data movement

### Animations

- **Fast** (0.15s): Small element changes
- **Medium** (0.3s): Panel transitions, tab changes
- **Slow** (0.5s): Full page transitions

## Implementation Notes

- Use CSS modules for component-scoped styling
- Use CSS variables for consistent theming
- Follow modified BEM naming convention
- Ensure sufficient contrast for accessibility
- Prioritize keyboard navigation and screen reader support