/* Color utility classes */

/* Text colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-accent { color: var(--text-accent); }
.text-warning { color: var(--text-warning); }
.text-danger { color: var(--text-danger); }
.text-success { color: var(--text-success); }
.text-info { color: var(--text-info); }

/* Neon text colors */
.text-neon-cyan { color: var(--neon-cyan); }
.text-neon-pink { color: var(--neon-pink); }
.text-neon-purple { color: var(--neon-purple); }
.text-neon-yellow { color: var(--neon-yellow); }
.text-neon-green { color: var(--neon-green); }
.text-neon-blue { color: var(--neon-blue); }

/* Background colors */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-elevated { background-color: var(--bg-elevated); }
.bg-accent { background-color: var(--bg-accent); }
.bg-hover { background-color: var(--bg-hover); }

/* Special backgrounds */
.bg-card { background-color: var(--bg-card); }
.bg-modal { background-color: var(--bg-modal); }
.bg-overlay { background-color: var(--bg-overlay); }
.bg-tooltip { background-color: var(--bg-tooltip); }

/* Background neon colors (low opacity) */
.bg-neon-cyan { background-color: var(--neon-cyan-muted); }
.bg-neon-pink { background-color: var(--neon-pink-muted); }
.bg-neon-purple { background-color: var(--neon-purple-muted); }
.bg-neon-yellow { background-color: var(--neon-yellow-muted); }
.bg-neon-green { background-color: var(--neon-green-muted); }

/* Gradient backgrounds */
.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-secondary { background: var(--gradient-secondary); }
.bg-gradient-warn { background: var(--gradient-warn); }
.bg-gradient-success { background: var(--gradient-success); }
.bg-gradient-info { background: var(--gradient-info); }
.bg-gradient-dark { background: var(--gradient-dark); }

/* Text gradients */
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.text-gradient-warning {
  background: var(--gradient-warn);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

/* Borders */
.border { border: var(--border-width) var(--border-style) var(--border-color); }
.border-t { border-top: var(--border-width) var(--border-style) var(--border-color); }
.border-r { border-right: var(--border-width) var(--border-style) var(--border-color); }
.border-b { border-bottom: var(--border-width) var(--border-style) var(--border-color); }
.border-l { border-left: var(--border-width) var(--border-style) var(--border-color); }

.border-active { border-color: var(--border-active); }
.border-cyan { border-color: var(--neon-cyan); }
.border-pink { border-color: var(--neon-pink); }
.border-purple { border-color: var(--neon-purple); }
.border-yellow { border-color: var(--neon-yellow); }

/* Shadows */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-neon { box-shadow: var(--shadow-neon); }
.shadow-pink { box-shadow: var(--shadow-pink); }
.shadow-purple { box-shadow: var(--shadow-purple); }
.shadow-yellow { box-shadow: var(--shadow-yellow); }
.shadow-green { box-shadow: var(--shadow-green); }

/* Border radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-full { border-radius: 9999px; }
