.missionPage {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  background: linear-gradient(90deg, #00F0FF, #0088FF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.filters {
  display: flex;
  gap: 12px;
}

.filterButton {
  padding: 8px 16px;
  background-color: #2A2A2A;
  border: 1px solid #333333;
  border-radius: 4px;
  color: #E0E0E0;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.filterButton:hover {
  background-color: #333333;
}

.filterButton.active {
  background-color: #00F0FF;
  border-color: #00F0FF;
  color: #1A1A1A;
}

.content {
  flex: 1;
  overflow-y: auto;
}

.listContainer, .detailContainer {
  height: 100%;
}
