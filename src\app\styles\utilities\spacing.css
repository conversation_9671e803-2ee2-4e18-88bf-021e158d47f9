/* Spacing utility classes */

/* Margin */
.m-0 { margin: 0; }
.m-xs { margin: var(--space-xs); }
.m-sm { margin: var(--space-sm); }
.m-md { margin: var(--space-md); }
.m-lg { margin: var(--space-lg); }
.m-xl { margin: var(--space-xl); }
.m-2xl { margin: var(--space-2xl); }

/* Margin top */
.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--space-xs); }
.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }

/* <PERSON>gin right */
.mr-0 { margin-right: 0; }
.mr-xs { margin-right: var(--space-xs); }
.mr-sm { margin-right: var(--space-sm); }
.mr-md { margin-right: var(--space-md); }
.mr-lg { margin-right: var(--space-lg); }
.mr-xl { margin-right: var(--space-xl); }
.mr-2xl { margin-right: var(--space-2xl); }

/* Margin bottom */
.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--space-xs); }
.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }

/* Margin left */
.ml-0 { margin-left: 0; }
.ml-xs { margin-left: var(--space-xs); }
.ml-sm { margin-left: var(--space-sm); }
.ml-md { margin-left: var(--space-md); }
.ml-lg { margin-left: var(--space-lg); }
.ml-xl { margin-left: var(--space-xl); }
.ml-2xl { margin-left: var(--space-2xl); }

/* Margin x-axis */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: var(--space-xs); margin-right: var(--space-xs); }
.mx-sm { margin-left: var(--space-sm); margin-right: var(--space-sm); }
.mx-md { margin-left: var(--space-md); margin-right: var(--space-md); }
.mx-lg { margin-left: var(--space-lg); margin-right: var(--space-lg); }
.mx-xl { margin-left: var(--space-xl); margin-right: var(--space-xl); }
.mx-2xl { margin-left: var(--space-2xl); margin-right: var(--space-2xl); }
.mx-auto { margin-left: auto; margin-right: auto; }

/* Margin y-axis */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: var(--space-xs); margin-bottom: var(--space-xs); }
.my-sm { margin-top: var(--space-sm); margin-bottom: var(--space-sm); }
.my-md { margin-top: var(--space-md); margin-bottom: var(--space-md); }
.my-lg { margin-top: var(--space-lg); margin-bottom: var(--space-lg); }
.my-xl { margin-top: var(--space-xl); margin-bottom: var(--space-xl); }
.my-2xl { margin-top: var(--space-2xl); margin-bottom: var(--space-2xl); }

/* Padding */
.p-0 { padding: 0; }
.p-xs { padding: var(--space-xs); }
.p-sm { padding: var(--space-sm); }
.p-md { padding: var(--space-md); }
.p-lg { padding: var(--space-lg); }
.p-xl { padding: var(--space-xl); }
.p-2xl { padding: var(--space-2xl); }

/* Padding top */
.pt-0 { padding-top: 0; }
.pt-xs { padding-top: var(--space-xs); }
.pt-sm { padding-top: var(--space-sm); }
.pt-md { padding-top: var(--space-md); }
.pt-lg { padding-top: var(--space-lg); }
.pt-xl { padding-top: var(--space-xl); }
.pt-2xl { padding-top: var(--space-2xl); }

/* Padding right */
.pr-0 { padding-right: 0; }
.pr-xs { padding-right: var(--space-xs); }
.pr-sm { padding-right: var(--space-sm); }
.pr-md { padding-right: var(--space-md); }
.pr-lg { padding-right: var(--space-lg); }
.pr-xl { padding-right: var(--space-xl); }
.pr-2xl { padding-right: var(--space-2xl); }

/* Padding bottom */
.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: var(--space-xs); }
.pb-sm { padding-bottom: var(--space-sm); }
.pb-md { padding-bottom: var(--space-md); }
.pb-lg { padding-bottom: var(--space-lg); }
.pb-xl { padding-bottom: var(--space-xl); }
.pb-2xl { padding-bottom: var(--space-2xl); }

/* Padding left */
.pl-0 { padding-left: 0; }
.pl-xs { padding-left: var(--space-xs); }
.pl-sm { padding-left: var(--space-sm); }
.pl-md { padding-left: var(--space-md); }
.pl-lg { padding-left: var(--space-lg); }
.pl-xl { padding-left: var(--space-xl); }
.pl-2xl { padding-left: var(--space-2xl); }

/* Padding x-axis */
.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: var(--space-xs); padding-right: var(--space-xs); }
.px-sm { padding-left: var(--space-sm); padding-right: var(--space-sm); }
.px-md { padding-left: var(--space-md); padding-right: var(--space-md); }
.px-lg { padding-left: var(--space-lg); padding-right: var(--space-lg); }
.px-xl { padding-left: var(--space-xl); padding-right: var(--space-xl); }
.px-2xl { padding-left: var(--space-2xl); padding-right: var(--space-2xl); }

/* Padding y-axis */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: var(--space-xs); padding-bottom: var(--space-xs); }
.py-sm { padding-top: var(--space-sm); padding-bottom: var(--space-sm); }
.py-md { padding-top: var(--space-md); padding-bottom: var(--space-md); }
.py-lg { padding-top: var(--space-lg); padding-bottom: var(--space-lg); }
.py-xl { padding-top: var(--space-xl); padding-bottom: var(--space-xl); }
.py-2xl { padding-top: var(--space-2xl); padding-bottom: var(--space-2xl); }

/* Gaps */
.gap-0 { gap: 0; }
.gap-xs { gap: var(--space-xs); }
.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }
.gap-2xl { gap: var(--space-2xl); }

/* Column Gap */
.column-gap-xs { column-gap: var(--space-xs); }
.column-gap-sm { column-gap: var(--space-sm); }
.column-gap-md { column-gap: var(--space-md); }
.column-gap-lg { column-gap: var(--space-lg); }

/* Row Gap */
.row-gap-xs { row-gap: var(--space-xs); }
.row-gap-sm { row-gap: var(--space-sm); }
.row-gap-md { row-gap: var(--space-md); }
.row-gap-lg { row-gap: var(--space-lg); }
