/**
 * Interfaces for the specialist/team system
 */

import { Specialist, SpecializationType, SkillType, Team, TrainingProgram } from './types';

/**
 * Repository interface for specialist data
 */
export interface SpecialistRepository {
  getAvailableSpecialists(): Promise<Specialist[]>;
  getHiredSpecialists(): Promise<Specialist[]>;
  getSpecialistById(id: string): Promise<Specialist | null>;
  hireSpecialist(id: string): Promise<Specialist>;
  fireSpecialist(id: string): Promise<boolean>;
  updateSpecialist(id: string, updates: Partial<Specialist>): Promise<Specialist>;
  generateSpecialists(count: number): Promise<Specialist[]>;
}

/**
 * Repository interface for team management
 */
export interface TeamRepository {
  getAllTeams(): Promise<Team[]>;
  getTeamById(id: string): Promise<Team | null>;
  createTeam(name: string, memberIds: string[]): Promise<Team>;
  updateTeam(id: string, updates: Partial<Team>): Promise<Team>;
  deleteTeam(id: string): Promise<boolean>;
}

/**
 * Repository interface for training programs
 */
export interface TrainingRepository {
  getAvailablePrograms(): Promise<TrainingProgram[]>;
  getProgramById(id: string): Promise<TrainingProgram | null>;
  enrollSpecialist(specialistId: string, programId: string): Promise<boolean>;
  completeTraining(specialistId: string, programId: string): Promise<Specialist>;
}
