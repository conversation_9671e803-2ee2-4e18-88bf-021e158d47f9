import React from 'react';
import styles from './Card.module.css';
import clsx from 'clsx';

export interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'outlined' | 'subtle';
  accent?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'info' | 'none';
  isClickable?: boolean;
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  variant = 'default',
  accent = 'none',
  isClickable = false,
  onClick,
}) => {
  const cardClass = clsx(
    styles.card,
    styles[`variant-${variant}`],
    accent !== 'none' && styles[`accent-${accent}`],
    isClickable && styles.clickable,
    className
  );

  return (
    <div className={cardClass} onClick={isClickable ? onClick : undefined}>
      <div className={styles.content}>{children}</div>
    </div>
  );
};

export interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
  extra?: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className,
  title,
  subtitle,
  extra,
}) => {
  return (
    <div className={clsx(styles.header, className)}>
      {(title || subtitle) ? (
        <div className={styles.headerContent}>
          {title && <h3 className={styles.title}>{title}</h3>}
          {subtitle && <div className={styles.subtitle}>{subtitle}</div>}
        </div>
      ) : (
        children
      )}
      {extra && <div className={styles.extra}>{extra}</div>}
    </div>
  );
};

export interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

export const CardBody: React.FC<CardBodyProps> = ({ children, className }) => {
  return <div className={clsx(styles.body, className)}>{children}</div>;
};

export interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right' | 'space-between';
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className,
  align = 'left',
}) => {
  return (
    <div
      className={clsx(
        styles.footer,
        styles[`align-${align}`],
        className
      )}
    >
      {children}
    </div>
  );
};
