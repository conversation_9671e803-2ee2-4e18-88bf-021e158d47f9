/* TrainingCenter styles */

.trainingCenter {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header styling */
.headerTitle {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.headerIcon {
  color: var(--color-accent-primary);
}

.headerSubtitle {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.badgeIcon {
  margin-right: 4px;
}

/* Search and filters */
.searchContainer {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.searchWrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  border-radius: var(--border-radius-sm);
  background-color: var(--color-bg-tertiary);
  padding: 0 var(--space-sm);
  border: 1px solid var(--color-border-primary);
  transition: all 0.2s ease;
}

.searchWrapper:focus-within {
  border-color: var(--color-accent-primary);
  box-shadow: 0 0 0 2px rgba(0, 240, 255, 0.2);
}

.searchIcon {
  color: var(--color-text-secondary);
  flex-shrink: 0;
}

.searchInput {
  flex: 1;
  background-color: transparent;
  border: none;
  color: var(--color-text-primary);
  font-family: inherit;
  font-size: var(--font-size-sm);
  padding: var(--space-xs) var(--space-sm);
  outline: none;
  width: 100%;
}

.searchInput::placeholder {
  color: var(--color-text-disabled);
}

.filterButton {
  flex-shrink: 0;
}

.dropdownIcon {
  transition: transform 0.2s ease;
}

.dropdownIconOpen {
  transform: rotate(180deg);
}

/* Filter panel */
.filterPanel {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--border-radius-sm);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.filterSection {
  margin-bottom: var(--space-md);
}

.filterSection:last-of-type {
  margin-bottom: var(--space-sm);
}

.filterTitle {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-sm);
  font-family: var(--font-mono);
}

.filterOptions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.filterOption {
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.filterOption:hover {
  background-color: rgba(0, 240, 255, 0.1);
  color: var(--color-text-primary);
}

.filterOptionActive {
  background-color: rgba(0, 240, 255, 0.15);
  color: var(--color-accent-primary);
  border-color: rgba(0, 240, 255, 0.3);
}

.resetFilterButton {
  margin-top: var(--space-sm);
}

/* Programs list */
.programsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  overflow-y: auto;
  padding-right: var(--space-xs);
}

/* Scrollbar styling */
.programsList::-webkit-scrollbar {
  width: 6px;
}

.programsList::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.programsList::-webkit-scrollbar-thumb {
  background: var(--color-accent-primary);
  border-radius: 3px;
}

.programsList::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-primary-hover);
}

/* Program card styles */
.programCard {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid var(--color-border-primary);
  overflow: hidden;
  position: relative;
}

.programCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.programCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--color-accent-primary);
  transform: scaleY(0);
  transform-origin: bottom;
  transition: transform 0.3s ease;
}

.programCard:hover::before {
  transform: scaleY(1);
}

.unavailableProgram {
  opacity: 0.7;
}

.unavailableProgram::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    45deg,
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.1) 10px,
    rgba(0, 0, 0, 0.2) 10px,
    rgba(0, 0, 0, 0.2) 20px
  );
  pointer-events: none;
  z-index: 1;
}

.unaffordableProgram::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    -45deg,
    rgba(255, 0, 0, 0.03),
    rgba(255, 0, 0, 0.03) 10px,
    rgba(255, 0, 0, 0.06) 10px,
    rgba(255, 0, 0, 0.06) 20px
  );
  pointer-events: none;
  z-index: 1;
}

.programTitle {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  position: relative;
}

.programIcon {
  color: var(--color-accent-primary);
}

.levelBadge {
  margin-left: var(--space-xs);
}

.programStats {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-top: 4px;
}

.programStat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
}

.statIcon {
  color: var(--color-text-secondary);
  opacity: 0.8;
}

.affordableCost {
  color: var(--color-success);
}

.unaffordableCost {
  color: var(--color-error);
}

.programDescription {
  margin: 0 0 var(--space-md);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.5;
}

/* Skill improvements */
.skillImprovements {
  margin-bottom: var(--space-md);
  padding: var(--space-sm);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
}

.improvementsTitle {
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin: 0 0 var(--space-sm);
  color: var(--color-text-primary);
  padding-bottom: var(--space-xs);
  border-bottom: 1px solid var(--color-border-primary);
}

.skillCategory {
  margin-bottom: var(--space-sm);
}

.skillCategory:last-child {
  margin-bottom: 0;
}

.categoryTitle {
  font-size: var(--font-size-xs);
  font-weight: 600;
  margin: 0 0 var(--space-xs);
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
}

.skillsList {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs) var(--space-md);
}

.skillItem {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-xs);
}

.skillName {
  color: var(--color-text-primary);
}

.skillImprovement {
  display: flex;
  align-items: center;
  color: var(--color-success);
  font-weight: 600;
  font-family: var(--font-mono);
}

.improvementIcon {
  margin-right: 2px;
}

.enrollButtonContainer {
  display: flex;
  justify-content: flex-end;
}

.enrollButton {
  position: relative;
  overflow: hidden;
}

.enrollButton::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.enrollButton:not(:disabled):hover::after {
  opacity: 1;
}

/* Loading state */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--space-xl);
  text-align: center;
}

.loadingSpinner {
  margin-bottom: var(--space-md);
  position: relative;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loadingSpinner::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border: 2px solid var(--color-accent-primary);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.loadingIcon {
  color: var(--color-accent-primary);
  opacity: 0.8;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loadingText {
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
}

/* Empty state */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
  text-align: center;
}

.emptyIcon {
  color: var(--color-text-disabled);
  margin-bottom: var(--space-md);
  opacity: 0.7;
}

.emptyText {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-md);
  font-family: var(--font-mono);
}

/* Responsive design */
@media (max-width: 768px) {
  .searchContainer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filterButton {
    align-self: flex-end;
  }
  
  .programStats {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }
  
  .skillsList {
    flex-direction: column;
    gap: var(--space-xs);
  }
}