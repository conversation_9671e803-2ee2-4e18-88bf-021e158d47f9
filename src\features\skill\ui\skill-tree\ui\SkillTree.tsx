import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardBody, Badge, But<PERSON>, Toolt<PERSON> } from '@/shared/ui';
import styles from './SkillTree.module.css';
import {
  IconBrain,
  IconUsers,
  IconDatabase,
  IconEye,
  IconBuildingBank,
  IconLock,
  IconChevronRight,
  IconCircleCheck,
  IconInfoCircle,
  IconCoin,
  IconArrowNarrowRight,
  IconChartBar
} from '@tabler/icons-react';
import clsx from 'clsx';

// Types for the skill tree
export interface SkillNode {
  id: string;
  name: string;
  description: string;
  category: 'technical' | 'social' | 'intelligence' | 'physical';
  level: number;
  cost: number;
  requiredSkills: string[];
  unlocks: string[];
  skillBonus: Record<string, number>;
  isUnlocked?: boolean;
  isAvailable?: boolean;
}

export interface SkillTreeProps {
  skills: SkillNode[];
  unlockedSkills: string[];
  availablePoints: number;
  onUnlockSkill: (skillId: string) => void;
  className?: string;
  title?: string;
}

const SkillTree: React.FC<SkillTreeProps> = ({
  skills,
  unlockedSkills,
  availablePoints,
  onUnlockSkill,
  className,
  title = 'Skill Tree'
}) => {
  const [selectedSkill, setSelectedSkill] = useState<SkillNode | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  // Process skills to add isUnlocked and isAvailable properties
  const processedSkills = skills.map(skill => {
    const isUnlocked = unlockedSkills.includes(skill.id);
    const isAvailable = !isUnlocked && skill.requiredSkills.every(reqSkill => 
      unlockedSkills.includes(reqSkill)
    );
    
    return {
      ...skill,
      isUnlocked,
      isAvailable
    };
  });
  
  // Group skills by category
  const skillsByCategory: Record<string, SkillNode[]> = {
    technical: [],
    social: [],
    intelligence: [],
    physical: []
  };
  
  processedSkills.forEach(skill => {
    skillsByCategory[skill.category].push(skill);
  });
  
  // Get skills to display based on selected category
  const skillsToDisplay = selectedCategory === 'all' 
    ? processedSkills 
    : skillsByCategory[selectedCategory] || [];
  
  // Get category icon
  const getCategoryIcon = (category: string, size = 20) => {
    switch (category) {
      case 'technical':
        return <IconBrain size={size} stroke={1.5} />;
      case 'social':
        return <IconUsers size={size} stroke={1.5} />;
      case 'intelligence':
        return <IconEye size={size} stroke={1.5} />;
      case 'physical':
        return <IconBuildingBank size={size} stroke={1.5} />;
      default:
        return <IconDatabase size={size} stroke={1.5} />;
    }
  };
  
  // Get category name
  const getCategoryName = (category: string): string => {
    switch (category) {
      case 'technical': return 'Technical';
      case 'social': return 'Social';
      case 'intelligence': return 'Intelligence';
      case 'physical': return 'Physical';
      default: return category;
    }
  };
  
  // Calculate skill coordinates in the tree
  const getSkillPositionStyles = (skill: SkillNode, index: number, total: number) => {
    // For simplicity, we're positioning skills in a grid-like structure
    // A more advanced implementation would use a force-directed graph or similar
    
    const level = skill.level;
    const levelSpacing = 150; // Horizontal spacing between levels
    const nodeSpacing = 80; // Vertical spacing between nodes
    
    // Count skills at the same level
    const skillsAtLevel = skillsToDisplay.filter(s => s.level === level).length;
    const positionAtLevel = skillsToDisplay.filter(s => s.level === level).findIndex(s => s.id === skill.id);
    
    const x = level * levelSpacing;
    const y = (positionAtLevel - (skillsAtLevel - 1) / 2) * nodeSpacing;
    
    return {
      left: `${x}px`,
      top: `${y}px`
    };
  };
  
  // Render connection lines between skills
  const renderConnections = () => {
    const connections: JSX.Element[] = [];
    
    skillsToDisplay.forEach(skill => {
      if (skill.requiredSkills.length > 0) {
        skill.requiredSkills.forEach(reqId => {
          const requiredSkill = skillsToDisplay.find(s => s.id === reqId);
          if (!requiredSkill) return;
          
          // Calculate positions of the two skills
          const startSkillPos = document.getElementById(`skill-${reqId}`)?.getBoundingClientRect();
          const endSkillPos = document.getElementById(`skill-${skill.id}`)?.getBoundingClientRect();
          
          if (startSkillPos && endSkillPos && startSkillPos.right < endSkillPos.left) {
            // Simple straight line for now - could be enhanced with curves
            const isUnlocked = unlockedSkills.includes(reqId) && unlockedSkills.includes(skill.id);
            const isPartiallyUnlocked = unlockedSkills.includes(reqId) && !unlockedSkills.includes(skill.id);
            
            connections.push(
              <div 
                key={`${reqId}-${skill.id}`}
                className={clsx(
                  styles.connectionLine,
                  isUnlocked && styles.unlockedConnection,
                  isPartiallyUnlocked && styles.partiallyUnlockedConnection
                )}
                style={{
                  width: `${endSkillPos.left - startSkillPos.right}px`,
                  height: '2px',
                  left: `${startSkillPos.right}px`,
                  top: `${startSkillPos.top + startSkillPos.height / 2}px`,
                  transform: `rotate(${Math.atan2(
                    endSkillPos.top + endSkillPos.height / 2 - (startSkillPos.top + startSkillPos.height / 2),
                    endSkillPos.left - startSkillPos.right
                  )}rad)`,
                  transformOrigin: '0 0'
                }}
              />
            );
          }
        });
      }
    });
    
    return connections;
  };
  
  // Render skill details panel
  const renderSkillDetails = () => {
    if (!selectedSkill) return null;
    
    const isUnlocked = unlockedSkills.includes(selectedSkill.id);
    const isAvailable = !isUnlocked && selectedSkill.requiredSkills.every(reqSkill => 
      unlockedSkills.includes(reqSkill)
    );
    const canAfford = availablePoints >= selectedSkill.cost;
    const canUnlock = isAvailable && canAfford;
    
    return (
      <div className={styles.skillDetailsPanel}>
        <div className={styles.skillDetailHeader}>
          <div className={styles.skillDetailTitle}>
            {getCategoryIcon(selectedSkill.category, 18)}
            <h3 className={styles.skillName}>{selectedSkill.name}</h3>
          </div>
          
          <div className={styles.skillStatus}>
            {isUnlocked ? (
              <Badge colorScheme="success" variant="subtle" size="sm">
                <IconCircleCheck size={14} className={styles.badgeIcon} stroke={1.5} />
                Unlocked
              </Badge>
            ) : isAvailable ? (
              <Badge colorScheme="info" variant="subtle" size="sm">
                <IconInfoCircle size={14} className={styles.badgeIcon} stroke={1.5} />
                Available
              </Badge>
            ) : (
              <Badge colorScheme="secondary" variant="subtle" size="sm">
                <IconLock size={14} className={styles.badgeIcon} stroke={1.5} />
                Locked
              </Badge>
            )}
          </div>
        </div>
        
        <p className={styles.skillDescription}>{selectedSkill.description}</p>
        
        <div className={styles.skillDetailSection}>
          <h4 className={styles.sectionTitle}>Requirements</h4>
          {selectedSkill.requiredSkills.length > 0 ? (
            <div className={styles.requirementsList}>
              {selectedSkill.requiredSkills.map(reqId => {
                const requiredSkill = skills.find(s => s.id === reqId);
                if (!requiredSkill) return null;
                
                const isReqUnlocked = unlockedSkills.includes(reqId);
                
                return (
                  <div 
                    key={reqId} 
                    className={clsx(
                      styles.requirementItem,
                      isReqUnlocked && styles.metRequirement
                    )}
                  >
                    {isReqUnlocked ? (
                      <IconCircleCheck size={16} className={styles.requirementIcon} stroke={1.5} />
                    ) : (
                      <IconLock size={16} className={styles.requirementIcon} stroke={1.5} />
                    )}
                    <span className={styles.requirementName}>{requiredSkill.name}</span>
                  </div>
                );
              })}
            </div>
          ) : (
            <p className={styles.noRequirements}>No prerequisites</p>
          )}
        </div>
        
        <div className={styles.skillDetailSection}>
          <h4 className={styles.sectionTitle}>Skill Bonuses</h4>
          <div className={styles.bonusList}>
            {Object.entries(selectedSkill.skillBonus).map(([skill, bonus]) => (
              <div key={skill} className={styles.bonusItem}>
                <IconChartBar size={16} className={styles.bonusIcon} stroke={1.5} />
                <span className={styles.bonusName}>
                  {skill.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </span>
                <span className={styles.bonusValue}>+{bonus}</span>
              </div>
            ))}
          </div>
        </div>
        
        <div className={styles.skillDetailSection}>
          <h4 className={styles.sectionTitle}>Unlocks Skills</h4>
          {selectedSkill.unlocks.length > 0 ? (
            <div className={styles.unlocksList}>
              {selectedSkill.unlocks.map(unlockId => {
                const unlockedSkill = skills.find(s => s.id === unlockId);
                if (!unlockedSkill) return null;
                
                return (
                  <div 
                    key={unlockId} 
                    className={styles.unlockItem}
                    onClick={() => setSelectedSkill(unlockedSkill)}
                  >
                    <IconChevronRight size={16} className={styles.unlockIcon} stroke={1.5} />
                    <span className={styles.unlockName}>{unlockedSkill.name}</span>
                  </div>
                );
              })}
            </div>
          ) : (
            <p className={styles.noUnlocks}>Terminal skill (unlocks nothing)</p>
          )}
        </div>
        
        <div className={styles.unlockButtonContainer}>
          {!isUnlocked && (
            <div className={styles.costIndicator}>
              <IconCoin size={16} className={clsx(styles.costIcon, {
                [styles.affordableCost]: canAfford,
                [styles.unaffordableCost]: !canAfford
              })} stroke={1.5} />
              <span className={clsx(styles.costValue, {
                [styles.affordableCost]: canAfford,
                [styles.unaffordableCost]: !canAfford
              })}>
                {selectedSkill.cost} points
              </span>
            </div>
          )}
          
          <Button
            variant={canUnlock ? 'primary' : 'secondary'}
            rightIcon={<IconArrowNarrowRight size={16} stroke={1.5} />}
            onClick={() => canUnlock && onUnlockSkill(selectedSkill.id)}
            disabled={isUnlocked || !canUnlock}
          >
            {isUnlocked 
              ? 'Already Unlocked' 
              : !isAvailable 
                ? 'Prerequisites Required' 
                : !canAfford 
                  ? 'Insufficient Points' 
                  : 'Unlock Skill'}
          </Button>
        </div>
      </div>
    );
  };
  
  return (
    <Card className={clsx(styles.skillTree, className)} variant="default">
      <CardHeader 
        title={
          <div className={styles.headerTitle}>
            <IconBrainCircuit size={18} className={styles.headerIcon} stroke={1.5} />
            {title}
          </div>
        }
        subtitle={
          <div className={styles.headerPoints}>
            <Badge colorScheme="success" variant="subtle">
              <IconCoin size={14} className={styles.badgeIcon} stroke={1.5} />
              {availablePoints} Skill Points Available
            </Badge>
          </div>
        }
        action={
          <div className={styles.categoryFilters}>
            <Button 
              variant={selectedCategory === 'all' ? 'primary' : 'ghost'} 
              size="sm"
              onClick={() => setSelectedCategory('all')}
            >
              All
            </Button>
            
            {Object.keys(skillsByCategory).map(category => (
              <Button 
                key={category}
                variant={selectedCategory === category ? 'primary' : 'ghost'} 
                size="sm"
                leftIcon={getCategoryIcon(category, 16)}
                onClick={() => setSelectedCategory(category)}
              >
                {getCategoryName(category)}
              </Button>
            ))}
          </div>
        }
      />
      <CardBody>
        <div className={styles.skillTreeContent}>
          <div className={styles.treeContainer}>
            <div className={styles.treeView}>
              {/* The skill nodes */}
              {skillsToDisplay.map((skill, index) => (
                <Tooltip key={skill.id} content={skill.name} placement="top">
                  <div 
                    id={`skill-${skill.id}`}
                    className={clsx(
                      styles.skillNode,
                      skill.isUnlocked && styles.unlockedSkill,
                      skill.isAvailable && styles.availableSkill,
                      !skill.isUnlocked && !skill.isAvailable && styles.lockedSkill,
                      selectedSkill?.id === skill.id && styles.selectedSkill
                    )}
                    style={getSkillPositionStyles(skill, index, skillsToDisplay.length)}
                    onClick={() => setSelectedSkill(skill)}
                  >
                    <div className={styles.skillNodeIcon}>
                      {getCategoryIcon(skill.category)}
                    </div>
                    <div className={styles.skillLevel}>{skill.level}</div>
                  </div>
                </Tooltip>
              ))}
              
              {/* The connection lines */}
              {renderConnections()}
            </div>
          </div>
          
          <div className={styles.detailsContainer}>
            {selectedSkill ? (
              renderSkillDetails()
            ) : (
              <div className={styles.noSelectionMessage}>
                <IconInfoCircle size={32} className={styles.noSelectionIcon} stroke={1.5} />
                <p>Select a skill to view details</p>
              </div>
            )}
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default SkillTree;