import React from 'react';
import styles from './BottomBar.module.css';
import { useAppSelector, useAppDispatch } from '../../../app/store';
import { addTab, setActiveTab, closeTab } from '../../../features/tabs/model/slice';
import {
  IconLayoutDashboard,
  IconTerminal,
  IconTarget,
  IconShoppingCart,
  IconUsers,
  IconSearch
} from '@tabler/icons-react';

// App definitions
interface AppDefinition {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  type: string;
}

const apps: AppDefinition[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: <IconLayoutDashboard size={20} stroke={1.5} />,
    description: 'Main overview of your operation',
    type: 'dashboard'
  },
  {
    id: 'terminal',
    name: 'Terminal',
    icon: <IconTerminal size={20} stroke={1.5} />,
    description: 'Command line interface',
    type: 'terminal'
  },
  {
    id: 'missions',
    name: 'Missions',
    icon: <IconTarget size={20} stroke={1.5} />,
    description: 'Manage your operations',
    type: 'mission'
  },
  {
    id: 'market',
    name: 'Market',
    icon: <IconShoppingCart size={20} stroke={1.5} />,
    description: 'Buy and sell resources',
    type: 'market'
  },
  {
    id: 'team',
    name: 'Team',
    icon: <IconUsers size={20} stroke={1.5} />,
    description: 'Manage your specialists',
    type: 'team'
  },
  {
    id: 'intel',
    name: 'Intel',
    icon: <IconSearch size={20} stroke={1.5} />,
    description: 'Analyze gathered intelligence',
    type: 'intel'
  }
];

const BottomBar: React.FC = () => {
  const dispatch = useAppDispatch();
  const { tabs } = useAppSelector(state => state.tabs);
  
  // Get active tab IDs
  const activeTabIds = tabs.map(tab => tab.id);
  
  const handleAppClick = (app: AppDefinition) => {
    // Check if this app is already open in a tab
    if (activeTabIds.includes(app.id)) {
      // If it's already open, just make it active
      dispatch(setActiveTab(app.id));
    } else {
      // Check if we already have 4 tabs open
      if (tabs.length >= 4) {
        console.warn('Maximum of 4 tabs allowed at once');
        // Here you would show a notification to the user
        return;
      }
      
      // Otherwise, open a new tab for this app
      dispatch(addTab({
        id: app.id,
        title: app.name,
        type: app.type,
        closable: app.id !== 'dashboard', // Dashboard can't be closed
        icon: app.icon
      }));
    }
  };
  
  // Close tab if it's active and user clicks the app button again with Alt key
  const handleAppKeyClick = (e: React.MouseEvent, app: AppDefinition) => {
    if (e.altKey && activeTabIds.includes(app.id) && app.id !== 'dashboard') {
      e.preventDefault();
      dispatch(closeTab(app.id));
    } else {
      handleAppClick(app);
    }
  };
  
  return (
    <div className={styles.bottomBar}>
      <div className={styles.appLauncher}>
        {apps.map(app => (
          <button
            key={app.id}
            className={`${styles.appButton} ${activeTabIds.includes(app.id) ? styles.active : ''}`}
            onClick={(e) => handleAppKeyClick(e, app)}
            title={`${app.name}: ${app.description}${app.id !== 'dashboard' ? ' (Alt+Click to close)' : ''}`}
          >
            <div className={`${styles.iconWrapper} ${activeTabIds.includes(app.id) ? styles.active : ''}`}>
              {app.icon}
            </div>
            <span className={styles.appName}>{app.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default BottomBar;