// Core color theme for CyberClash
export const theme = {
  // Base colors
  colors: {
    background: {
      primary: '#1A1A1A',
      secondary: '#2A2A2A',
      tertiary: '#333333'
    },
    text: {
      primary: '#E0E0E0',
      secondary: '#B0B0B0',
      disabled: '#707070'
    },
    accent: {
      primary: '#00F0FF',
      secondary: '#0088FF',
      tertiary: '#00DDBB'
    },
    status: {
      normal: '#007AFF',
      warning: '#FF9500',
      critical: '#FF2D55',
      secure: '#34C759',
      unknown: '#AF52DE'
    },
    feedback: {
      success: '#28CD41',
      error: '#FF3B30',
      alert: '#FF9500'
    }
  },
  
  // Typography
  typography: {
    fontFamily: {
      base: '\"Inter\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif',
      monospace: '\"JetBrains Mono\", \"SF Mono\", Monaco, Menlo, Consolas, monospace'
    },
    fontSize: {
      xs: '10px',
      sm: '12px',
      md: '14px',
      lg: '16px',
      xl: '18px',
      xxl: '24px'
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      bold: 700
    }
  },
  
  // Spacing
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px'
  },
  
  // Borders and radius
  borders: {
    radius: {
      sm: '4px',
      md: '8px',
      lg: '12px',
      round: '50%'
    },
    width: {
      thin: '1px',
      medium: '2px',
      thick: '4px'
    }
  },
  
  // Shadows
  shadows: {
    sm: '0 2px 4px rgba(0, 0, 0, 0.1)',
    md: '0 4px 8px rgba(0, 0, 0, 0.12)',
    lg: '0 8px 16px rgba(0, 0, 0, 0.14)',
    glow: '0 0 8px'
  },
  
  // Transitions
  transitions: {
    fast: '0.15s ease',
    medium: '0.3s ease',
    slow: '0.5s ease'
  },
  
  // Z-index layers
  zIndex: {
    base: 1,
    dropdown: 10,
    sticky: 100,
    fixed: 200,
    modal: 300,
    tooltip: 400,
    notification: 500
  }
};

// Also export as default for compatibility
export default theme;
