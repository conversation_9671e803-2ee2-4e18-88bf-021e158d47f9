import { Command, CommandResult, CommandContext } from '../types';

const whoamiCommand: Command = {
  help: 'Displays the current user name.\nUsage: whoami',
  
  execute(args: string[], context: CommandContext): CommandResult {
    // Get user from environment variables or use default
    const user = context.environmentVariables?.USER || 'user';
    
    return { 
      output: user, 
      status: 'success' 
    };
  }
};

export { whoamiCommand };
