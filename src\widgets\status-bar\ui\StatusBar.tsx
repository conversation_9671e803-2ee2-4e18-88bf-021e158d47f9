import React from 'react';
import styles from './StatusBar.module.css';

const StatusBar: React.FC = () => {
  // Format numbers with commas for thousands
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };
  
  return (
    <div className={styles.statusBar}>
      <div className={styles.statusSection}>
        <span className={styles.resourceIcon}>💰</span>
        <span className={styles.resourceLabel}>Credits:</span>
        <span className={styles.resourceValue}>{formatNumber(1000)}</span>
      </div>
      
      <div className={styles.statusSection}>
        <span className={styles.resourceIcon}>💾</span>
        <span className={styles.resourceLabel}>Data:</span>
        <span className={styles.resourceValue}>{formatNumber(0)}</span>
      </div>
      
      <div className={styles.statusSection}>
        <span className={styles.resourceIcon}>🔒</span>
        <span className={styles.resourceLabel}>Security:</span>
        <span className={styles.resourceValue}>100%</span>
      </div>
      
      <div className={styles.statusSection}>
        <span className={styles.systemStatus}>System Status: Operational</span>
      </div>
    </div>
  );
};

export default StatusBar;
