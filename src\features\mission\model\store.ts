import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  Mission, 
  MissionProgress, 
  MissionFilter,
  MissionSuccess,
  MissionFailure
} from '@/core/mission/types';
import { missionService } from '@/core/mission';
import { MissionState } from './types';

interface MissionActions {
  // Async actions
  fetchMissions: (filter?: MissionFilter) => Promise<void>;
  startMission: (missionId: string) => Promise<void>;
  completeObjective: (missionId: string, objectiveId: string) => Promise<void>;
  
  // Sync actions
  setActiveMission: (missionId: string | null) => void;
  updateMissionProgress: (missionId: string, progress: Partial<MissionProgress>) => void;
  resetMissionState: () => void;
  
  // Internal state management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

type MissionStore = MissionState & MissionActions;

const initialState: MissionState = {
  missions: {},
  availableMissions: [],
  inProgressMissions: [],
  completedMissions: [],
  failedMissions: [],
  activeMissionId: null,
  missionProgress: {},
  isLoading: false,
  error: null
};

export const useMissionStore = create<MissionStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      fetchMissions: async (filter?: MissionFilter) => {
        set({ isLoading: true, error: null });
        
        try {
          const missions = await missionService.getMissions(filter);
          
          // Organize missions by status
          const missionMap: Record<string, Mission> = {};
          const availableMissions: string[] = [];
          const inProgressMissions: string[] = [];
          const completedMissions: string[] = [];
          const failedMissions: string[] = [];
          
          missions.forEach(mission => {
            missionMap[mission.id] = mission;
            
            switch (mission.status) {
              case 'available':
                availableMissions.push(mission.id);
                break;
              case 'in_progress':
                inProgressMissions.push(mission.id);
                break;
              case 'completed':
                completedMissions.push(mission.id);
                break;
              case 'failed':
                failedMissions.push(mission.id);
                break;
            }
          });
          
          set({
            missions: missionMap,
            availableMissions,
            inProgressMissions,
            completedMissions,
            failedMissions,
            isLoading: false
          });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to fetch missions' 
          });
        }
      },
      
      startMission: async (missionId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const result = await missionService.startMission(missionId);
          const { missions, availableMissions, inProgressMissions, missionProgress } = get();
          
          // Update mission status
          const updatedMissions = {
            ...missions,
            [missionId]: {
              ...missions[missionId],
              status: 'in_progress' as const
            }
          };
          
          // Move mission from available to in-progress
          const newAvailableMissions = availableMissions.filter(id => id !== missionId);
          const newInProgressMissions = [...inProgressMissions, missionId];
          
          // Add initial progress
          const newMissionProgress = {
            ...missionProgress,
            [missionId]: result.progress
          };
          
          set({
            missions: updatedMissions,
            availableMissions: newAvailableMissions,
            inProgressMissions: newInProgressMissions,
            missionProgress: newMissionProgress,
            isLoading: false
          });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to start mission' 
          });
        }
      },
      
      completeObjective: async (missionId: string, objectiveId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const result = await missionService.completeObjective(missionId, objectiveId);
          const { missionProgress } = get();
          
          // Update mission progress
          const newMissionProgress = {
            ...missionProgress,
            [missionId]: result.progress
          };
          
          set({
            missionProgress: newMissionProgress,
            isLoading: false
          });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to complete objective' 
          });
        }
      },
      
      setActiveMission: (missionId: string | null) => {
        set({ activeMissionId: missionId });
      },
      
      updateMissionProgress: (missionId: string, progress: Partial<MissionProgress>) => {
        const { missionProgress } = get();
        
        const updatedProgress = {
          ...missionProgress,
          [missionId]: {
            ...missionProgress[missionId],
            ...progress,
            missionId // Ensure we keep mission ID
          }
        };
        
        set({ missionProgress: updatedProgress });
      },
      
      resetMissionState: () => {
        set(initialState);
      },
      
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
      
      setError: (error: string | null) => {
        set({ error });
      },
    }),
    {
      name: 'mission-store',
    }
  )
);

// Selectors for easier access to derived state
export const useMissionSelectors = () => {
  const store = useMissionStore();
  
  return {
    // Basic selectors
    missions: store.missions,
    availableMissions: store.availableMissions.map(id => store.missions[id]).filter(Boolean),
    inProgressMissions: store.inProgressMissions.map(id => store.missions[id]).filter(Boolean),
    completedMissions: store.completedMissions.map(id => store.missions[id]).filter(Boolean),
    failedMissions: store.failedMissions.map(id => store.missions[id]).filter(Boolean),
    activeMission: store.activeMissionId ? store.missions[store.activeMissionId] : null,
    activeMissionProgress: store.activeMissionId ? store.missionProgress[store.activeMissionId] : null,
    isLoading: store.isLoading,
    error: store.error,
    
    // Helper functions
    getMissionById: (id: string) => store.missions[id] || null,
    getMissionProgress: (id: string) => store.missionProgress[id] || null,
  };
};
