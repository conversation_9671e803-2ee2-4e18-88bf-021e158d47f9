// Terminal module exports
import { TerminalService } from './TerminalService';
import { ITerminalService } from './interfaces';
import { ReduxTerminalRepository, MockFileSystemRepository } from '../../features/terminal/repository';

// Create repositories
const terminalRepository = new ReduxTerminalRepository();
const fileSystemRepository = new MockFileSystemRepository();

// Create and export terminal service
export const terminalService: ITerminalService = new TerminalService(
  fileSystemRepository,
  terminalRepository
);

// Export types
export * from './types';
export * from './interfaces';
