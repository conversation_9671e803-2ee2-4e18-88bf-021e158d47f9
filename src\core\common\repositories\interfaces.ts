// Common repository interfaces
import { CommandResult, CommandContext, FileSystemState, FileSystemEntry } from '../../terminal/types';
import { Mission, MissionProgress, MissionStatus, ObjectiveStatus } from '../../mission/types';

export interface TerminalRepository {
  getCommandHistory(instanceId: string): string[];
  addCommandToHistory(instanceId: string, command: string): void;
  getCurrentDirectory(instanceId: string): string;
  setCurrentDirectory(instanceId: string, directory: string): void;
}

export interface FileSystemRepository {
  getFileSystem(): FileSystemState;
  getEntry(path: string): FileSystemEntry | undefined;
  createEntry(path: string, entry: FileSystemEntry): void;
  updateEntry(path: string, changes: Partial<FileSystemEntry>): void;
  deleteEntry(path: string): void;
  doesPathExist(path: string): boolean;
  resolvePath(currentDirectory: string, path: string): string;
}

// Mission-related repositories
export interface MissionRepository {
  // Mission CRUD operations
  getAvailableMissions(): Promise<Mission[]>;
  getMissionById(id: string): Promise<Mission | null>;
  saveMission(mission: Mission): Promise<void>;
  updateMissionStatus(id: string, status: MissionStatus): Promise<void>;
  deleteMission(id: string): Promise<void>;
  
  // Objective management
  updateObjectiveStatus(missionId: string, objectiveId: string, status: ObjectiveStatus): Promise<void>;
  
  // Progress tracking
  getMissionProgress(missionId: string): Promise<MissionProgress | null>;
  saveMissionProgress(missionId: string, progress: MissionProgress): Promise<void>;
  
  // Time tracking
  setMissionStartTime(missionId: string, timestamp: number): Promise<void>;
  setMissionCompletionTime(missionId: string, timestamp: number): Promise<void>;
  setMissionFailureTime(missionId: string, timestamp: number): Promise<void>;
  
  // Mission search and filtering
  getMissionsByFaction(factionId: string): Promise<Mission[]>;
  getMissionsByStatus(status: MissionStatus): Promise<Mission[]>;
  getMissionsByType(type: string): Promise<Mission[]>;
}

// User-related repositories (for future implementation)
export interface UserRepository {
  getCurrentUser(): Promise<any | null>;
  updateUserResources(resources: Record<string, number>): Promise<void>;
  updateUserReputation(factions: Record<string, number>): Promise<void>;
}

// Specialist-related repositories (for future implementation)
export interface SpecialistRepository {
  getHiredSpecialists(): Promise<any[]>;
  getAvailableSpecialists(): Promise<any[]>;
  hireSpecialist(id: string): Promise<void>;
  assignSpecialist(id: string, missionId: string): Promise<void>;
}
