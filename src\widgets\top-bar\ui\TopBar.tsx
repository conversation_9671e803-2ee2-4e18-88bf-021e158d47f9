import React, { useState, useEffect } from 'react';
import styles from './TopBar.module.css';
import {
  IconBell,
  IconClock,
  IconCalendar,
  IconSettings,
  IconShield,
  IconCoin
} from '@tabler/icons-react';

// Widget definitions
interface Widget {
  id: string;
  name: string;
  component: React.ReactNode;
  enabled: boolean;
}

// Example widgets
const TimeWidget: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    
    return () => clearInterval(timer);
  }, []);
  
  const formattedTime = currentTime.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
  
  return (
    <div className={styles.timeWidget}>
      <IconClock size={16} stroke={1.5} />
      <span>{formattedTime}</span>
    </div>
  );
};

const DateWidget: React.FC = () => {
  const currentDate = new Date();
  
  const formattedDate = currentDate.toLocaleDateString([], { 
    weekday: 'short',
    month: 'short', 
    day: 'numeric' 
  });
  
  return (
    <div className={styles.dateWidget}>
      <IconCalendar size={16} stroke={1.5} />
      <span>{formattedDate}</span>
    </div>
  );
};

const SecurityLevelWidget: React.FC = () => {
  return (
    <div className={styles.securityWidget}>
      <IconShield size={16} stroke={1.5} />
      <span>100%</span>
    </div>
  );
};

const CreditsWidget: React.FC = () => {
  return (
    <div className={styles.creditsWidget}>
      <IconCoin size={16} stroke={1.5} />
      <span>1,000</span>
    </div>
  );
};

const availableWidgets: Widget[] = [
  { id: 'time', name: 'Time', component: <TimeWidget />, enabled: true },
  { id: 'date', name: 'Date', component: <DateWidget />, enabled: true },
  { id: 'security', name: 'Security Level', component: <SecurityLevelWidget />, enabled: true },
  { id: 'credits', name: 'Credits', component: <CreditsWidget />, enabled: true },
];

const TopBar: React.FC = () => {
  const [widgets, setWidgets] = useState(availableWidgets);
  const [showNotifications, setShowNotifications] = useState(false);
  const [hasUnreadNotifications, setHasUnreadNotifications] = useState(true);
  const [isConfiguring, setIsConfiguring] = useState(false);
  
  const toggleWidget = (widgetId: string) => {
    setWidgets(prev => prev.map(widget => 
      widget.id === widgetId ? { ...widget, enabled: !widget.enabled } : widget
    ));
  };
  
  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
    if (hasUnreadNotifications) {
      setHasUnreadNotifications(false);
    }
  };
  
  return (
    <div className={styles.topBar}>
      <div className={styles.brand}>
        <span className={styles.logo}>⚡</span>
        <span className={styles.name}>CyberClash</span>
      </div>
      
      <div className={styles.widgetsArea}>
        {widgets.filter(w => w.enabled).map(widget => (
          <div key={widget.id} className={styles.widget}>
            {widget.component}
          </div>
        ))}
      </div>
      
      <div className={styles.actionArea}>
        <button 
          className={`${styles.actionButton} ${isConfiguring ? styles.active : ''}`}
          onClick={() => setIsConfiguring(!isConfiguring)}
          title="Configure Widgets"
        >
          <div className={styles.iconWrapper}>
            <IconSettings size={18} stroke={1.5} />
          </div>
        </button>
        
        <button 
          className={`${styles.actionButton} ${showNotifications ? styles.active : ''}`}
          onClick={toggleNotifications}
          title="Notifications"
        >
          <div className={styles.iconWrapper}>
            <IconBell size={18} stroke={1.5} />
            {hasUnreadNotifications && <div className={styles.notificationDot}></div>}
          </div>
        </button>
      </div>
      
      {/* Configuration Panel */}
      {isConfiguring && (
        <div className={styles.configPanel}>
          <h3>Configure Widgets</h3>
          <ul className={styles.widgetList}>
            {widgets.map(widget => (
              <li key={widget.id} className={styles.widgetItem}>
                <label className={styles.widgetToggle}>
                  <input 
                    type="checkbox" 
                    checked={widget.enabled}
                    onChange={() => toggleWidget(widget.id)}
                  />
                  <span className={styles.widgetName}>{widget.name}</span>
                </label>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default TopBar;