import React, { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../../app/store';
import { 
  fetchSpecialists, 
  setActiveSpecialist, 
  hireSpecialist, 
  fireSpecialist 
} from '../../../features/specialist/model/slice';
import { 
  selectAvailableSpecialists, 
  selectHiredSpecialists, 
  selectIsLoading,
  selectActiveSpecialist
} from '../../../features/specialist/model/selectors';
import { addNotification } from '../../../features/notifications';
import { Specialist, SpecialistStatus } from '@/core/specialist/types';
import { SpecialistDetail } from '../../../widgets/specialist-detail';
import { TrainingCenter } from '../../../widgets/training-center';
import styles from './TeamPage.module.css';

interface TeamPageProps {
  tabId: string;
}

// Sample training programs
const SAMPLE_PROGRAMS = [
  {
    id: 'training-1',
    name: 'Network Infiltration Basics',
    description: 'Learn the fundamentals of network penetration and access techniques.',
    type: 'technical',
    duration: 5,
    cost: 2500,
    skillImprovements: [
      { skill: 'network_infiltration', bonus: 10 },
      { skill: 'hacking', bonus: 5 }
    ]
  },
  {
    id: 'training-2',
    name: 'Advanced Social Engineering',
    description: 'Master the art of human manipulation and deception techniques.',
    type: 'field',
    duration: 7,
    cost: 3800,
    skillImprovements: [
      { skill: 'social_engineering', bonus: 15 },
      { skill: 'persuasion', bonus: 10 },
      { skill: 'deception', bonus: 8 }
    ],
    requiredLevel: 2
  },
  {
    id: 'training-3',
    name: 'Cryptanalysis Masterclass',
    description: 'Advanced training in breaking encryption and secure communications.',
    type: 'technical',
    duration: 10,
    cost: 5000,
    skillImprovements: [
      { skill: 'cryptography', bonus: 20 },
      { skill: 'intel_analysis', bonus: 5 }
    ],
    requiredLevel: 3
  },
  {
    id: 'training-4',
    name: 'OSINT Techniques',
    description: 'Open-source intelligence gathering and analysis methods.',
    type: 'intelligence',
    duration: 4,
    cost: 1800,
    skillImprovements: [
      { skill: 'osint', bonus: 15 },
      { skill: 'intel_analysis', bonus: 8 }
    ]
  }
];

const TeamPage: React.FC<TeamPageProps> = ({ tabId }) => {
  const dispatch = useAppDispatch();
  
  // Specialist data from store
  const availableSpecialists = useAppSelector(selectAvailableSpecialists);
  const hiredSpecialists = useAppSelector(selectHiredSpecialists);
  const isLoading = useAppSelector(selectIsLoading);
  const activeSpecialist = useAppSelector(selectActiveSpecialist);
  
  // View state: 'hire', 'team', 'training', 'detail'
  const [view, setView] = useState<'hire' | 'team' | 'training' | 'detail'>('hire');
  
  // Fetch specialists on component mount
  useEffect(() => {
    dispatch(fetchSpecialists({}));
  }, [dispatch]);
  
  // Handle specialist selection
  const handleSpecialistSelect = (id: string) => {
    dispatch(setActiveSpecialist(id));
    setView('detail');
  };
  
  // Handle return to list view
  const handleBackToList = () => {
    dispatch(setActiveSpecialist(null));
    setView('hire');
  };
  
  // Handle hiring a specialist
  const handleHireSpecialist = (id: string, event?: React.MouseEvent) => {
    if (event) event.stopPropagation(); // Prevent card click if from button
    
    dispatch(hireSpecialist({ specialistId: id }))
      .unwrap()
      .then((specialist) => {
        dispatch(addNotification({
          type: 'success',
          title: 'Specialist Hired',
          message: `${specialist.name} has joined your team.`,
        }));
      })
      .catch((error) => {
        dispatch(addNotification({
          type: 'error',
          title: 'Failed to Hire',
          message: error || 'An error occurred while hiring the specialist.',
        }));
      });
  };
  
  // Handle firing a specialist
  const handleFireSpecialist = (id: string, event?: React.MouseEvent) => {
    if (event) event.stopPropagation(); // Prevent card click if from button
    
    // Find the specialist name for the notification
    const specialist = [...availableSpecialists, ...hiredSpecialists].find(s => s.id === id);
    
    dispatch(fireSpecialist({ specialistId: id }))
      .unwrap()
      .then(() => {
        dispatch(addNotification({
          type: 'info',
          title: 'Specialist Released',
          message: specialist ? `${specialist.name} has been released from your team.` : 'Specialist has been released from your team.',
        }));
        
        // If we're in detail view of the fired specialist, go back to list
        if (view === 'detail' && activeSpecialist?.id === id) {
          handleBackToList();
        }
      })
      .catch((error) => {
        dispatch(addNotification({
          type: 'error',
          title: 'Failed to Release',
          message: error || 'An error occurred while releasing the specialist.',
        }));
      });
  };
  
  // Handle training a specialist
  const handleTrainSpecialist = (id: string) => {
    // This will be implemented later
    dispatch(addNotification({
      type: 'info',
      title: 'Training Coming Soon',
      message: 'Specialist training will be available in a future update.',
    }));
    
    // Navigate to training view
    setView('training');
  };
  
  // Handle enrolling in training
  const handleEnrollInTraining = (programId: string) => {
    dispatch(addNotification({
      type: 'info',
      title: 'Training Coming Soon',
      message: 'Training enrollment will be available in a future update.',
    }));
  };
  
  // Handle assigning a specialist to a mission
  const handleAssignSpecialist = (id: string) => {
    // This will be implemented later
    dispatch(addNotification({
      type: 'info',
      title: 'Mission Assignment Coming Soon',
      message: 'Mission assignment will be available in a future update.',
    }));
  };
  
  // Get CSS class for status
  const getStatusClass = (status: SpecialistStatus): string => {
    switch (status) {
      case 'available': return styles.statusAvailable;
      case 'hired': return styles.statusHired;
      case 'on_mission': return styles.statusOnMission;
      case 'training': return styles.statusTraining;
      default: return '';
    }
  };
  
  // Format a specialization string for display
  const formatSpecialization = (spec: string): string => {
    return spec.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };
  
  // Generate initials from name
  const getInitials = (name: string): string => {
    return name.split(' ')
      .map(part => part.charAt(0).toUpperCase())
      .join('');
  };
  
  // Render a specialist card
  const renderSpecialistCard = (specialist: Specialist) => {
    const { id, name, specialization, skills, attributes, status, cost } = specialist;
    
    // Get top 3 skills
    const topSkills = Object.entries(skills)
      .sort(([, valueA], [, valueB]) => valueB - valueA)
      .slice(0, 3);
    
    return (
      <div 
        key={id} 
        className={styles.specialistCard}
        onClick={() => handleSpecialistSelect(id)}
      >
        <div className={styles.cardHeader}>
          <div className={styles.avatar}>
            {getInitials(name)}
          </div>
          <div className={styles.specialistInfo}>
            <h3 className={styles.specialistName}>{name}</h3>
            <p className={styles.specialization}>{formatSpecialization(specialization)}</p>
          </div>
          <span className={`${styles.status} ${getStatusClass(status)}`}>
            {status.replace('_', ' ')}
          </span>
        </div>
        
        <div className={styles.skills}>
          {topSkills.map(([skill, value]) => (
            <div key={skill} className={styles.skillRow}>
              <span className={styles.skillName}>{formatSpecialization(skill)}</span>
              <span className={styles.skillValue}>{value}</span>
            </div>
          ))}
        </div>
        
        <div className={styles.attributes}>
          <div className={styles.attribute}>
            <span className={styles.attributeLabel}>R:</span>
            <span className={styles.attributeValue}>{attributes.reliability}</span>
          </div>
          <div className={styles.attribute}>
            <span className={styles.attributeLabel}>D:</span>
            <span className={styles.attributeValue}>{attributes.discretion}</span>
          </div>
          <div className={styles.attribute}>
            <span className={styles.attributeLabel}>E:</span>
            <span className={styles.attributeValue}>{attributes.efficiency}</span>
          </div>
          <div className={styles.attribute}>
            <span className={styles.attributeLabel}>L:</span>
            <span className={styles.attributeValue}>{attributes.loyalty}</span>
          </div>
        </div>
        
        <div className={styles.cardFooter}>
          <div className={styles.cost}>${cost.toLocaleString()}</div>
          {status === 'available' ? (
            <button 
              className={styles.hireButton}
              onClick={(e) => handleHireSpecialist(id, e)}
            >
              Hire Specialist
            </button>
          ) : (
            <button 
              className={styles.fireButton}
              onClick={(e) => handleFireSpecialist(id, e)}
            >
              Fire Specialist
            </button>
          )}
        </div>
      </div>
    );
  };
  
  // Main content based on view
  const renderContent = () => {
    if (view === 'detail' && activeSpecialist) {
      return (
        <SpecialistDetail 
          specialist={activeSpecialist}
          onBack={handleBackToList}
          onHire={handleHireSpecialist}
          onFire={handleFireSpecialist}
          onTrain={handleTrainSpecialist}
          onAssign={handleAssignSpecialist}
        />
      );
    }
    
    if (view === 'training') {
      return (
        <>
          <div className={styles.tabsContainer}>
            <button 
              className={`${styles.tabButton} ${view === 'hire' ? styles.active : ''}`}
              onClick={() => setView('hire')}
            >
              Hire Specialists
            </button>
            <button 
              className={`${styles.tabButton} ${view === 'team' ? styles.active : ''}`}
              onClick={() => setView('team')}
            >
              My Team
            </button>
            <button 
              className={`${styles.tabButton} ${view === 'training' ? styles.active : ''}`}
              onClick={() => setView('training')}
            >
              Training
            </button>
          </div>
          
          <TrainingCenter 
            programs={SAMPLE_PROGRAMS}
            activeTrainings={[]}
            onEnroll={handleEnrollInTraining}
          />
        </>
      );
    }
    
    return (
      <>
        <div className={styles.tabsContainer}>
          <button 
            className={`${styles.tabButton} ${view === 'hire' ? styles.active : ''}`}
            onClick={() => setView('hire')}
          >
            Hire Specialists
          </button>
          <button 
            className={`${styles.tabButton} ${view === 'team' ? styles.active : ''}`}
            onClick={() => setView('team')}
          >
            My Team
          </button>
          <button 
            className={`${styles.tabButton} ${view === 'training' ? styles.active : ''}`}
            onClick={() => setView('training')}
          >
            Training
          </button>
        </div>
        
        <div className={styles.content}>
          {view === 'hire' && (
            <div className={styles.listGrid}>
              {isLoading ? (
                <div>Loading specialists...</div>
              ) : availableSpecialists.length > 0 ? (
                availableSpecialists.map(renderSpecialistCard)
              ) : (
                <div>No specialists available for hire.</div>
              )}
            </div>
          )}
          
          {view === 'team' && (
            <div className={styles.listGrid}>
              {isLoading ? (
                <div>Loading team...</div>
              ) : hiredSpecialists.length > 0 ? (
                hiredSpecialists.map(renderSpecialistCard)
              ) : (
                <div>You haven't hired any specialists yet.</div>
              )}
            </div>
          )}
        </div>
      </>
    );
  };
  
  return (
    <div className={styles.teamPage}>
      <div className={styles.header}>
        <h1 className={styles.title}>Team Management</h1>
      </div>
      
      {renderContent()}
    </div>
  );
};

export default TeamPage;
