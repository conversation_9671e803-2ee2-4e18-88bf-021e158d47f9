# CyberClash Documentation

Welcome to the CyberClash documentation. This folder contains comprehensive documentation about the game's architecture, features, and implementation plans.

## Contents

### Architecture & Structure
- [Architecture Guide](./architecture-guide.md) - Comprehensive guide to the project's architecture
- [Features](./features.md) - Documentation of game features and systems
- [Styling System](./styling-system.md) - CSS architecture and visual design guidelines

### Game Systems
- [Marketplace System](./marketplace-system.md) - Trading and economy systems
- [Mission System](./mission-system.md) - Mission structure and mechanics
- [Operational Security](./operational-security.md) - Security mechanics and implementation
- [Progression System](./progression-system.md) - Character and skill advancement
- [Team Management](./team-management.md) - Team building and specialist management

### Planning & Development
- [Plan](./plan.md) - Implementation roadmap and development phases
- [Advanced Gameplay](./advanced-gameplay.md) - Future gameplay features
- [Backend Migration](./backend-migration.md) - Plan for backend integration

## Getting Started

If you're new to the project, start by reading the Architecture Guide to understand the project structure, then review the Plan to see our development roadmap.

## Contributing

When contributing to the project, please follow the architecture patterns described in these documents. The separation between Core game logic and UI/state management is crucial for maintainability and future backend migration.
