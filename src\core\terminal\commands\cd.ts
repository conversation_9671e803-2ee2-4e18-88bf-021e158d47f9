import { Command, CommandResult, CommandContext } from '../types';
import { FileSystemRepository } from '../../common/repositories/interfaces';

export function cdCommand(fileSystemRepo: FileSystemRepository): Command {
  return {
    help: 'Changes the current directory.\nUsage: cd [directory]',
    
    execute(args: string[], context: CommandContext): CommandResult {
      if (args.length === 0) {
        // Default to home directory
        const homeDir = context.environmentVariables?.HOME || '/home/<USER>';
        return { 
          output: '', 
          status: 'success',
          newDirectory: homeDir
        };
      }
      
      const path = args[0];
      let newDir = fileSystemRepo.resolvePath(context.currentDirectory, path);
      
      // Check if directory exists
      const entry = fileSystemRepo.getEntry(newDir);
      
      if (!entry) {
        return {
          output: `cd: no such directory: ${path}`,
          status: 'error'
        };
      }
      
      if (entry.type !== 'directory') {
        return {
          output: `cd: not a directory: ${path}`,
          status: 'error'
        };
      }
      
      // Return empty output but include the new directory to change to
      return { 
        output: '', 
        status: 'success',
        newDirectory: newDir
      };
    }
  };
}
