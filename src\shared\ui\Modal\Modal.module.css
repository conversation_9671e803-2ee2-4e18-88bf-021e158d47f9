.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

.modalContainer {
  position: relative;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  animation: scaleIn 0.2s ease-out;
  max-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
}

/* Modal sizes */
.size-sm {
  width: 300px;
}

.size-md {
  width: 500px;
}

.size-lg {
  width: 700px;
}

.size-xl {
  width: 900px;
}

.size-full {
  width: calc(100vw - 40px);
  height: calc(100vh - 40px);
}

/* Modal variants */
.variant-default {
  background-color: var(--bg-secondary);
}

.variant-cyberpunk {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-active);
  box-shadow: var(--shadow-neon);
  overflow: visible;
}

.variant-cyberpunk::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-primary);
  z-index: -1;
  border-radius: calc(var(--border-radius-lg) + 2px);
  opacity: 0.5;
  animation: pulse var(--animation-pulse);
}

.variant-cyberpunk::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(
    to bottom right,
    rgba(0, 240, 255, 0.05) 0%,
    transparent 40%,
    transparent 60%,
    rgba(0, 240, 255, 0.05) 100%
  );
  pointer-events: none;
}

/* Modal scroll behavior */
.scroll-inside .modalContent {
  overflow-y: auto;
}

.scroll-outside {
  overflow-y: auto;
}

/* Modal header */
.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-color);
}

.modalTitle {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.closeButton {
  margin-left: var(--space-md);
}

/* Modal content */
.modalContent {
  padding: var(--space-md);
  max-height: 70vh;
}

.scroll-inside .modalContent {
  max-height: 70vh;
  overflow-y: auto;
}

.scroll-outside .modalContent {
  max-height: none;
  overflow-y: visible;
}

/* Modal footer */
.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-sm);
  padding: var(--space-md);
  border-top: 1px solid var(--border-color);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}
