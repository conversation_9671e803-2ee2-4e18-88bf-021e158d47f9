import React from 'react';
import { useAppSelector } from '../../../app/store';
import { LayoutType } from '@/shared/types/common';
import styles from './TabContent.module.css';
import clsx from 'clsx';
import DashboardPage from '../../../pages/dashboard/ui/DashboardPage';
import MissionDashboard from '../../mission-dashboard';
import SpecialistManager from '../../specialist-manager';
import TerminalConsole from '../../terminal-console';
import MarketplaceBrowser from '../../marketplace-browser';
import TrainingCenter from '../../training-center';

// Map of tab types to their component implementations
const tabComponents: Record<string, React.FC<{ tabId: string }>> = {
  dashboard: ({ tabId }) => <DashboardPage tabId={tabId} />,
  terminal: ({ tabId }) => <TerminalConsole instanceId={tabId} />,
  mission: ({ tabId }) => <MissionDashboard showDetails={true} />,
  team: ({ tabId }) => <SpecialistManager />,
  market: ({ tabId }) => <MarketplaceBrowser />,
  training: ({ tabId }) => <TrainingCenter />,
  intel: ({ tabId }) => <div>Intelligence Tab {tabId}</div>,
  messaging: ({ tabId }) => <div>Messaging Tab {tabId}</div>,
  settings: ({ tabId }) => <div>Settings Tab {tabId}</div>,
};

interface TabContentProps {
  className?: string;
}

const TabContent: React.FC<TabContentProps> = ({ className }) => {
  const { tabs, activeTabId, layout } = useAppSelector(state => state.tabs);
  
  // Function to get CSS class based on layout and position
  const getLayoutClass = (layout: LayoutType, position: number, totalTabs: number): string => {
    if (layout === 'single' || totalTabs === 1) return styles.fullTab;
    
    if (layout === 'split' && totalTabs === 2) {
      return position === 0 ? styles.leftSplitTab : styles.rightSplitTab;
    }
    
    if (layout === 'triple' && totalTabs === 3) {
      if (position === 0) return styles.mainTripleTab;
      return position === 1 ? styles.topStackedTab : styles.bottomStackedTab;
    }
    
    if (layout === 'grid') {
      switch (position) {
        case 0: return styles.topLeftGridTab;
        case 1: return styles.topRightGridTab;
        case 2: return styles.bottomLeftGridTab;
        case 3: return styles.bottomRightGridTab;
        default: return '';
      }
    }
    
    return '';
  };
  
  // Only render visible tabs
  const visibleTabs = tabs.slice(0, 4); // Maximum of 4 tabs with grid layout
  
  return (
    <div className={styles.contentContainer}>
      <div className={clsx(styles.tabContent, styles[layout], className)}>
        {visibleTabs.map((tab, index) => {
          const TabComponent = tabComponents[tab.type] || (() => <div>Unknown tab type: {tab.type}</div>);
          
          return (
            <div 
              key={tab.id}
              className={clsx(
                styles.tabPane,
                getLayoutClass(layout, index, visibleTabs.length),
                tab.id === activeTabId && styles.activeTab
              )}
            >
              <TabComponent tabId={tab.id} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TabContent;