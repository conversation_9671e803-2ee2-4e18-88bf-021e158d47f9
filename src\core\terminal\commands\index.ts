import { helpCommand } from './help';
import { lsCommand } from './ls';
import { cdCommand } from './cd';
import { pwdCommand } from './pwd';
import { echoCommand } from './echo';
import { clearCommand } from './clear';
import { whoamiCommand } from './whoami';
import { dateCommand } from './date';
import { nmapCommand } from './nmap';
import { FileSystemRepository } from '../../common/repositories/interfaces';

export class TerminalCommands {
  static createCommands(fileSystemRepo: FileSystemRepository) {
    const commands = {
      help: helpCommand,
      ls: lsCommand(fileSystemRepo),
      cd: cdCommand(fileSystemRepo),
      pwd: pwdCommand,
      echo: echoCommand,
      clear: clearCommand,
      whoami: whoamiCommand,
      date: dateCommand,
      nmap: nmapCommand,
    };
    
    // Make help command aware of all available commands
    commands.help.setCommands(commands);
    
    return commands;
  }
}
