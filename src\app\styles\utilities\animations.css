/* Animation utility classes */

/* Import the base animations from the main animation file */

/* Preset animation durations */
.duration-fast { animation-duration: 0.2s; }
.duration-normal { animation-duration: 0.5s; }
.duration-slow { animation-duration: 1s; }
.duration-very-slow { animation-duration: 2s; }

/* Preset animation delays */
.delay-none { animation-delay: 0s; }
.delay-short { animation-delay: 0.1s; }
.delay-medium { animation-delay: 0.3s; }
.delay-long { animation-delay: 0.5s; }

/* Animation iteration counts */
.once { animation-iteration-count: 1; }
.twice { animation-iteration-count: 2; }
.infinite { animation-iteration-count: infinite; }

/* Animation directions */
.alternate { animation-direction: alternate; }
.reverse { animation-direction: reverse; }
.alternate-reverse { animation-direction: alternate-reverse; }

/* Animation fill modes */
.forwards { animation-fill-mode: forwards; }
.backwards { animation-fill-mode: backwards; }
.both { animation-fill-mode: both; }

/* Animation timing functions */
.linear { animation-timing-function: linear; }
.ease { animation-timing-function: ease; }
.ease-in { animation-timing-function: ease-in; }
.ease-out { animation-timing-function: ease-out; }
.ease-in-out { animation-timing-function: ease-in-out; }

/* Animation play states */
.paused { animation-play-state: paused; }
.running { animation-play-state: running; }

/* Combined animation utility classes */
.animated-glow {
  animation: glow 2s ease-in-out infinite;
}

.animated-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animated-float {
  animation: float 6s ease-in-out infinite;
}

.animated-flicker {
  animation: flicker 3s linear infinite;
}

.animated-blink {
  animation: blink 1.5s infinite;
}

.animated-data-flow {
  animation: dataFlow 2s linear infinite;
}

.animated-scan {
  animation: scan 2s ease-in-out infinite alternate;
}

.animated-border-pulse {
  animation: border-pulse 2s ease-in-out infinite;
}

.animated-circuit-pulse {
  animation: circuitPulse 4s ease-in-out infinite;
}

.animated-color-cycle {
  animation: colorCycle 8s infinite;
}

.animated-gradient-cycle {
  animation: gradientCycle 10s ease infinite;
}

.animated-typing {
  animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite;
}

/* Animation hover triggers */
.hover-glow:hover {
  animation: glow 2s ease-in-out infinite;
}

.hover-glitch:hover {
  animation: glitch 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both infinite;
}

.hover-pulse:hover {
  animation: pulse 2s infinite;
}

.hover-float:hover {
  animation: float 6s ease-in-out infinite;
}

/* Transition utility classes */
.transition-fast {
  transition: all var(--transition-fast);
}

.transition-normal {
  transition: all var(--transition-normal);
}

.transition-slow {
  transition: all var(--transition-slow);
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
}

.transition-opacity {
  transition-property: opacity;
}

.transition-shadow {
  transition-property: box-shadow;
}

.transition-transform {
  transition-property: transform;
}

/* Transform utility classes */
.scale-hover:hover {
  transform: scale(1.05);
}

.translate-up-hover:hover {
  transform: translateY(-4px);
}

.rotate-hover:hover {
  transform: rotate(5deg);
}
