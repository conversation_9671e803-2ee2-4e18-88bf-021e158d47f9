/**
 * Specialist module exports
 */

import { SpecialistService } from './SpecialistService';
import {
  ZustandSpecialistRepository
} from '../../features/specialist/repository';

export * from './types';
export * from './interfaces';
export { SpecialistService } from './SpecialistService';

// Create repositories
const specialistRepository = new ZustandSpecialistRepository();
// TODO: Implement ZustandTeamRepository and ZustandTrainingRepository
// const teamRepository = new ZustandTeamRepository();
// const trainingRepository = new ZustandTrainingRepository();

// Initialize the service with repositories
export const specialistService = new SpecialistService(
  specialistRepository,
  teamRepository,
  trainingRepository
);
