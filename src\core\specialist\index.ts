/**
 * Specialist module exports
 */

import { SpecialistService } from './SpecialistService';
import { 
  ReduxSpecialistRepository,
  ReduxTeamRepository,
  ReduxTrainingRepository 
} from '../../features/specialist/repository';

export * from './types';
export * from './interfaces';
export { SpecialistService } from './SpecialistService';

// Create repositories
const specialistRepository = new ReduxSpecialistRepository();
const teamRepository = new ReduxTeamRepository();
const trainingRepository = new ReduxTrainingRepository();

// Initialize the service with repositories
export const specialistService = new SpecialistService(
  specialistRepository,
  teamRepository,
  trainingRepository
);
