# UI Component Update Plan

## Overview

This document tracks the progress of updating UI components to match the new design system with Tabler Icons and consistent styling.

## Core Layout Components

| Component | Status | Description |
|-----------|--------|-------------|
| AppShell | ✅ Updated | Main layout container with cyberpunk styling |
| TopBar | ✅ Updated | Header with configurable widgets and notification icon |
| BottomBar | ✅ Updated | App launcher with active state indicators |
| TabBar | ✅ Updated | Tab management with 4-tab limit and improved styling |
| TabContent | ✅ Updated | Content area for active application with improved layout and animations |

## Notification System

| Component | Status | Description |
|-----------|--------|-------------|
| NotificationList | ✅ Updated | Notification panel and alerts with TypeScript icons |
| NotificationItem | ✅ Updated | Individual notification styling with type indicators |

## Shared UI Components

| Component | Status | Description |
|-----------|--------|-------------|
| Button | ✅ Added | Versatile button component with multiple variants and states |
| Card | ✅ Added | Card layout component with header, body, and footer sections |
| Input | ✅ Added | Text input with various styles and validation states |
| Modal | ✅ Added | Configurable dialog component with multiple sizes |
| Badge | ✅ Added | Status indicator with multiple variants and color schemes |
| Tooltip | ✅ Added | Contextual information tooltips with different placements |

## Mission Components

| Component | Status | Description |
|-----------|--------|-------------|
| MissionCard | ✅ Updated | Card displaying mission information with enhanced cyberpunk styling |
| MissionDetail | ✅ Updated | Expanded mission information view with objectives and rewards |
| MissionList | ✅ Updated | List of available missions with filtering and sorting functionality |
| RewardDisplay | ✅ Added | Mission rewards visualization with different display modes |

## Specialist Components

| Component | Status | Description |
|-----------|--------|-------------|
| SpecialistDetail | ✅ Updated | Specialist profile with skills, traits, and history using Card components |
| SpecialistList | ✅ Added | List of specialists to manage with filtering functionality |
| SkillDisplay | ✅ Added | Visualization of specialist skills with categories and level indicators |

## Terminal Components

| Component | Status | Description |
|-----------|--------|-------------|
| Terminal | ✅ Updated | Command-line interface with cyberpunk styling and CRT effects |
| CommandPrompt | ✅ Updated | Input area for commands with improved UX |
| CommandOutput | ✅ Updated | Formatted command results with status indicators |

## Training Center

| Component | Status | Description |
|-----------|--------|-------------|
| TrainingCenter | ⏳ Pending | Learning and skill development interface |
| SkillTree | ⏳ Pending | Visual skill progression tree |

## Next Steps

1. **High Priority** (COMPLETED)
   - ✅ Update MissionCard using new shared Card component
   - ✅ Update Terminal interface as a central gameplay element
   - ✅ Implement MissionList using new shared components

2. **Medium Priority** (COMPLETED)
   - ✅ Update SpecialistDetail components using the new Card and Badge components
   - ✅ Update TabContent component with proper styling
   - ✅ Implement SpecialistList using the shared components

3. **Lower Priority**
   - ⏳ Implement TrainingCenter component
   - ✅ Update MissionDetail component
   - ✅ Implement SkillDisplay component
   - ✅ Implement RewardDisplay component

## Final Remaining Components
- ⏳ TrainingCenter
- ⏳ SkillTree

## Implementation Guidelines

1. **Use Shared Components**
   - Import UI components from `src/shared/ui` instead of creating custom ones
   - Follow the component prop patterns established in the shared components
   - Extend the components with additional styles as needed using composition

2. **Use Tabler Icons Consistently**
   - Import icons from `@tabler/icons-react`
   - Maintain uniform sizing (generally 18-20px with 1.5 stroke width)
   - Use appropriate icons that match their purpose

3. **Follow Style Conventions**
   - Use CSS variables for colors, spacing, and transitions
   - Implement responsive designs that work across screen sizes
   - Add appropriate hover, focus, and active states

4. **Maintain Cyberpunk Aesthetic**
   - Use neon accent colors for interactive elements
   - Implement subtle animation effects
   - Maintain the dark theme with high contrast text

## Examples

### Using the Button Component

```tsx
import { Button } from 'src/shared/ui';
import { IconSend } from '@tabler/icons-react';

// Primary button with icon
<Button 
  variant="primary"
  rightIcon={<IconSend size={18} stroke={1.5} />}
>
  Send Message
</Button>

// Ghost button for secondary actions
<Button variant="ghost" size="sm">Cancel</Button>
```

### Using the Card Component

```tsx
import { Card, CardHeader, CardBody, CardFooter, Button } from 'src/shared/ui';

<Card variant="elevated" accent="primary">
  <CardHeader title="Mission Details" subtitle="High Priority" />
  <CardBody>
    <p>Mission content goes here...</p>
  </CardBody>
  <CardFooter align="right">
    <Button variant="secondary">Decline</Button>
    <Button variant="primary">Accept</Button>
  </CardFooter>
</Card>
```