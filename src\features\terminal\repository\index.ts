import { TerminalRepository, FileSystemRepository } from '@/core/common/repositories/interfaces';
import { FileSystemEntry, FileSystemState } from '@/core/terminal/types';

// Implementation of TerminalRepository using Redux
export class ReduxTerminalRepository implements TerminalRepository {
  // This is a placeholder implementation until we have the actual Redux store setup
  private instances: Record<string, {
    history: string[];
    currentDirectory: string;
    commandIndex: number;
  }> = {
    'default': {
      history: [],
      currentDirectory: '/home/<USER>',
      commandIndex: -1,
    }
  };

  getCommandHistory(instanceId: string): string[] {
    const instance = this.instances[instanceId] || this.instances['default'];
    return instance.history;
  }
  
  addCommandToHistory(instanceId: string, command: string): void {
    // Ensure instance exists
    if (!this.instances[instanceId]) {
      this.instances[instanceId] = {
        history: [],
        currentDirectory: '/home/<USER>',
        commandIndex: -1,
      };
    }
    
    this.instances[instanceId].history.push(command);
    this.instances[instanceId].commandIndex = this.instances[instanceId].history.length;
  }
  
  getCurrentDirectory(instanceId: string): string {
    const instance = this.instances[instanceId] || this.instances['default'];
    return instance.currentDirectory;
  }
  
  setCurrentDirectory(instanceId: string, directory: string): void {
    if (!this.instances[instanceId]) {
      this.instances[instanceId] = {
        history: [],
        currentDirectory: '/home/<USER>',
        commandIndex: -1,
      };
    }
    
    this.instances[instanceId].currentDirectory = directory;
  }
}

// Mock file system implementation
export class MockFileSystemRepository implements FileSystemRepository {
  private fileSystem: FileSystemState = {
    entries: {
      '/': {
        name: '/',
        type: 'directory',
        children: ['/home', '/bin', '/etc', '/var'],
      },
      '/home': {
        name: 'home',
        type: 'directory',
        children: ['/home/<USER>'],
      },
      '/home/<USER>': {
        name: 'user',
        type: 'directory',
        children: ['/home/<USER>/documents', '/home/<USER>/downloads', '/home/<USER>/projects'],
      },
      '/home/<USER>/documents': {
        name: 'documents',
        type: 'directory',
        children: ['/home/<USER>/documents/notes.txt'],
      },
      '/home/<USER>/documents/notes.txt': {
        name: 'notes.txt',
        type: 'file',
        content: 'These are some notes about the security of target systems.',
      },
      '/home/<USER>/downloads': {
        name: 'downloads',
        type: 'directory',
        children: [],
      },
      '/home/<USER>/projects': {
        name: 'projects',
        type: 'directory',
        children: ['/home/<USER>/projects/cyberclash', '/home/<USER>/projects/tools'],
      },
      '/home/<USER>/projects/cyberclash': {
        name: 'cyberclash',
        type: 'directory',
        children: ['/home/<USER>/projects/cyberclash/README.md'],
      },
      '/home/<USER>/projects/cyberclash/README.md': {
        name: 'README.md',
        type: 'file',
        content: '# CyberClash\n\nA cybersecurity-themed strategy game with an OS-like interface.\n\n## Getting Started\n\nRun `npm install` and then `npm run dev` to start the development server.\n',
      },
      '/home/<USER>/projects/tools': {
        name: 'tools',
        type: 'directory',
        children: [
          '/home/<USER>/projects/tools/scan.py',
          '/home/<USER>/projects/tools/exploit.py',
          '/home/<USER>/projects/tools/analyze.py'
        ],
      },
      '/home/<USER>/projects/tools/scan.py': {
        name: 'scan.py',
        type: 'file',
        content: '#!/usr/bin/env python3\n\n# Network scanning tool\n# Usage: ./scan.py <target>\n\nimport sys\n\ndef main():\n    print("Scanning target...")',
      },
      '/home/<USER>/projects/tools/exploit.py': {
        name: 'exploit.py',
        type: 'file',
        content: '#!/usr/bin/env python3\n\n# Vulnerability exploitation tool\n# Usage: ./exploit.py <target> <vulnerability>',
      },
      '/home/<USER>/projects/tools/analyze.py': {
        name: 'analyze.py',
        type: 'file',
        content: '#!/usr/bin/env python3\n\n# Data analysis tool\n# Usage: ./analyze.py <data_file>',
      },
      '/bin': {
        name: 'bin',
        type: 'directory',
        children: [],
      },
      '/etc': {
        name: 'etc',
        type: 'directory',
        children: [],
      },
      '/var': {
        name: 'var',
        type: 'directory',
        children: [],
      },
    },
    rootDirectory: '/',
  };
  
  getFileSystem(): FileSystemState {
    return this.fileSystem;
  }
  
  getEntry(path: string): FileSystemEntry | undefined {
    return this.fileSystem.entries[path];
  }
  
  createEntry(path: string, entry: FileSystemEntry): void {
    this.fileSystem.entries[path] = entry;
    
    // Update parent directory
    const parts = path.split('/');
    parts.pop(); // Remove entry name
    const parentPath = parts.join('/') || '/';
    
    const parent = this.fileSystem.entries[parentPath];
    if (parent && parent.type === 'directory') {
      parent.children = parent.children || [];
      if (!parent.children.includes(path)) {
        parent.children.push(path);
      }
    }
  }
  
  updateEntry(path: string, changes: Partial<FileSystemEntry>): void {
    const entry = this.fileSystem.entries[path];
    if (entry) {
      this.fileSystem.entries[path] = { ...entry, ...changes };
    }
  }
  
  deleteEntry(path: string): void {
    if (!this.fileSystem.entries[path]) return;
    
    // Remove from parent's children
    const parts = path.split('/');
    parts.pop();
    const parentPath = parts.join('/') || '/';
    
    const parent = this.fileSystem.entries[parentPath];
    if (parent && parent.type === 'directory' && parent.children) {
      parent.children = parent.children.filter(child => child !== path);
    }
    
    // Delete the entry itself
    delete this.fileSystem.entries[path];
  }
  
  doesPathExist(path: string): boolean {
    return !!this.fileSystem.entries[path];
  }
  
  resolvePath(currentDirectory: string, path: string): string {
    // Handle special cases
    if (path === '~' || path === '') {
      return '/home/<USER>';
    }
    
    if (path === '.') {
      return currentDirectory;
    }
    
    if (path === '..') {
      const parts = currentDirectory.split('/').filter(Boolean);
      parts.pop();
      return parts.length === 0 ? '/' : `/${parts.join('/')}`;
    }
    
    // Handle absolute paths
    if (path.startsWith('/')) {
      return path;
    }
    
    // Handle relative paths
    let resolved = `${currentDirectory}/${path}`;
    resolved = resolved.replace(/\/+/g, '/'); // Remove duplicate slashes
    
    return resolved;
  }
}
