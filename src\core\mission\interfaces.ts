import { 
  Mission, 
  MissionFilter, 
  MissionSuccess, 
  MissionFailure,
  MissionProgress,
  Objective,
  MissionStatus
} from './types';

/**
 * Interface for Mission Service
 */
export interface IMissionService {
  /**
   * Get all available missions with optional filtering
   */
  getAvailableMissions(filter?: MissionFilter): Promise<Mission[]>;
  
  /**
   * Get a specific mission by ID
   */
  getMissionById(id: string): Promise<Mission | null>;
  
  /**
   * Start a mission
   */
  startMission(missionId: string): Promise<MissionProgress>;
  
  /**
   * Complete an objective in a mission
   */
  completeObjective(missionId: string, objectiveId: string): Promise<MissionProgress>;
  
  /**
   * Fail an objective in a mission
   */
  failObjective(missionId: string, objectiveId: string, reason: string): Promise<MissionProgress>;
  
  /**
   * Update mission progress
   */
  updateMissionProgress(missionId: string, progress: Partial<MissionProgress>): Promise<MissionProgress>;
  
  /**
   * Complete a mission
   */
  completeMission(missionId: string): Promise<MissionSuccess>;
  
  /**
   * Fail a mission
   */
  failMission(missionId: string, reason: string): Promise<MissionFailure>;
  
  /**
   * Check if an objective's dependencies are met
   */
  areObjectiveDependenciesMet(mission: Mission, objectiveId: string): boolean;
  
  /**
   * Get the next available objective in a mission
   */
  getNextObjective(mission: Mission): Objective | null;
  
  /**
   * Check if a mission is complete (all required objectives done)
   */
  isMissionComplete(mission: Mission): boolean;
  
  /**
   * Generate a new mission (for dynamic mission generation)
   */
  generateMission(params: Record<string, any>): Promise<Mission>;
}
