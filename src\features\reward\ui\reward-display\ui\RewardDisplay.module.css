/* RewardDisplay Styles */

.rewardDisplay {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Header styling */
.headerTitle {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.headerIcon {
  color: var(--color-warning);
}

/* Reward list */
.rewardList {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.rewardItem {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
  transition: transform 0.2s ease;
  position: relative;
  overflow: hidden;
}

.rewardItem:hover {
  transform: translateX(4px);
}

.rewardItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at top right, rgba(255, 255, 255, 0.03), transparent 70%);
  pointer-events: none;
}

.rewardIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--color-accent-primary);
}

.positiveIcon {
  color: var(--color-success);
}

.negativeIcon {
  color: var(--color-error);
}

.rewardContent {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.rewardValue {
  font-weight: 600;
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
  font-family: var(--font-mono);
}

.positiveValue {
  color: var(--color-success);
}

.negativeValue {
  color: var(--color-error);
}

.rewardLabel {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
}

/* Compact display */
.rewardDisplayCompact {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.compactBadge {
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 0;
  transition: transform 0.2s ease;
}

.compactBadge:hover {
  transform: translateY(-2px);
}

.badgeIcon {
  flex-shrink: 0;
}

/* Empty state */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-md);
  text-align: center;
}

.emptyIcon {
  color: var(--color-text-disabled);
  margin-bottom: var(--space-sm);
  opacity: 0.7;
}

.emptyText {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
  font-family: var(--font-mono);
}