import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TerminalCommand, TerminalState } from './types';

const initialState: TerminalState = {
  commands: [],
  outputs: [],
  instances: {
    // Default terminal instance
    'default': {
      history: [],
      currentDirectory: '/home/<USER>',
      commandIndex: -1,
    }
  }
};

export const terminalSlice = createSlice({
  name: 'terminal',
  initialState,
  reducers: {
    executeCommand: (state, action: PayloadAction<{ 
      command: string;
      instanceId?: string;
      commandId: string;
    }>) => {
      const { command, instanceId = 'default', commandId } = action.payload;
      
      // Ensure instance exists
      if (!state.instances[instanceId]) {
        state.instances[instanceId] = {
          history: [],
          currentDirectory: '/home/<USER>',
          commandIndex: -1,
        };
      }
      
      // Add command to global commands list
      state.commands.push({
        command,
        instanceId,
        commandId,
        timestamp: Date.now(),
      });
      
      // Add to instance history
      const instance = state.instances[instanceId];
      instance.history.push(command);
      instance.commandIndex = instance.history.length;
    },
    
    setCommandOutput: (state, action: PayloadAction<{
      commandId: string;
      output: string;
      status: 'success' | 'error' | 'info' | 'warning';
      instanceId?: string;
    }>) => {
      const { commandId, output, status, instanceId = 'default' } = action.payload;
      
      // Add to outputs list
      state.outputs.push({
        commandId,
        output,
        status,
        instanceId,
        timestamp: Date.now(),
      });
    },
    
    navigateHistory: (state, action: PayloadAction<{ 
      direction: 'up' | 'down';
      instanceId?: string;
    }>) => {
      const { direction, instanceId = 'default' } = action.payload;
      
      if (!state.instances[instanceId]) return;
      
      const instance = state.instances[instanceId];
      const historyLength = instance.history.length;
      
      if (direction === 'up') {
        // Move back in history (older commands)
        if (instance.commandIndex > 0) {
          instance.commandIndex--;
        }
      } else {
        // Move forward in history (newer commands)
        if (instance.commandIndex < historyLength) {
          instance.commandIndex++;
        }
      }
    },
    
    clearHistory: (state, action: PayloadAction<{ instanceId?: string }>) => {
      const { instanceId } = action.payload;
      
      if (instanceId) {
        // Clear specific instance
        if (state.instances[instanceId]) {
          state.instances[instanceId].history = [];
          state.instances[instanceId].commandIndex = -1;
          
          // Filter out commands and outputs for this instance
          state.commands = state.commands.filter(cmd => cmd.instanceId !== instanceId);
          state.outputs = state.outputs.filter(out => out.instanceId !== instanceId);
        }
      } else {
        // Clear all history
        state.commands = [];
        state.outputs = [];
        
        // Reset all instances
        Object.keys(state.instances).forEach(id => {
          state.instances[id].history = [];
          state.instances[id].commandIndex = -1;
        });
      }
    },
    
    clearTerminal: (state, action: PayloadAction<{ instanceId?: string }>) => {
      const { instanceId = 'default' } = action.payload;
      
      // Only clear the output display, keep command history
      state.commands = state.commands.filter(cmd => cmd.instanceId !== instanceId);
      state.outputs = state.outputs.filter(out => out.instanceId !== instanceId);
    },
    
    changeDirectory: (state, action: PayloadAction<{ 
      path: string;
      instanceId?: string;
    }>) => {
      const { path, instanceId = 'default' } = action.payload;
      
      if (state.instances[instanceId]) {
        state.instances[instanceId].currentDirectory = path;
      }
    },
  },
});

export const { 
  executeCommand, 
  setCommandOutput, 
  navigateHistory, 
  clearHistory,
  clearTerminal,
  changeDirectory
} = terminalSlice.actions;

export default terminalSlice.reducer;
