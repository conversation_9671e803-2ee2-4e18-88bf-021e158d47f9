import React, { useState } from 'react';
import { Specialist, SpecialistStatus, SpecializationType } from '@/core/specialist/types';
import { <PERSON>, CardHeader, CardBody, Input, Badge, Button } from '@/shared/ui';
import styles from './SpecialistList.module.css';
import {
  IconSearch,
  IconFilter,
  IconLoader,
  IconUsers,
  IconSortAscending,
  IconChevronDown,
  IconRefresh,
  IconX,
  IconCircleCheck,
  IconUser,
  IconChartBar,
  IconCoin,
  IconPlayerPlay,
  IconEye
} from '@tabler/icons-react';
import clsx from 'clsx';

export interface SpecialistListFilterProps {
  specialization?: SpecializationType | 'all';
  status?: SpecialistStatus | 'all';
  minSkill?: number;
  maxCost?: number;
  search?: string;
}

interface SpecialistListProps {
  specialists: Specialist[];
  onSpecialistSelect: (specialistId: string) => void;
  onFilterChange?: (filters: SpecialistListFilterProps) => void;
  filters?: SpecialistListFilterProps;
  loading?: boolean;
  emptyMessage?: string;
  showFilters?: boolean;
  className?: string;
  title?: string;
}

const SpecialistList: React.FC<SpecialistListProps> = ({
  specialists,
  onSpecialistSelect,
  onFilterChange,
  filters = {},
  loading = false,
  emptyMessage = 'No specialists available',
  showFilters = true,
  className,
  title = 'Available Specialists'
}) => {
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  
  // Format a specialization string for display
  const formatSpecialization = (str: string): string => {
    return str.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };
  
  // Get status details
  const getStatusDetails = (status: SpecialistStatus): {
    label: string;
    color: 'success' | 'info' | 'warning' | 'danger' | 'secondary';
  } => {
    switch (status) {
      case 'available':
        return { label: 'Available', color: 'success' };
      case 'hired':
        return { label: 'Hired', color: 'info' };
      case 'on_mission':
        return { label: 'On Mission', color: 'danger' };
      case 'training':
        return { label: 'Training', color: 'warning' };
      case 'unavailable':
        return { label: 'Unavailable', color: 'secondary' };
      default:
        return { label: status, color: 'secondary' };
    }
  };
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onFilterChange) {
      onFilterChange({
        ...filters,
        search: e.target.value
      });
    }
  };

  const handleFilterClear = () => {
    if (onFilterChange) {
      onFilterChange({
        specialization: 'all',
        status: 'all',
        minSkill: 0,
        maxCost: undefined,
        search: ''
      });
    }
  };
  
  const toggleFilterPanel = () => {
    setShowFilterPanel(!showFilterPanel);
  };

  const renderActiveFilters = () => {
    const activeFilters = [];
    
    if (filters.specialization && filters.specialization !== 'all') {
      activeFilters.push(
        <Badge key="specialization" colorScheme="info" variant="subtle" size="sm">
          <span className={styles.badgeContent}>
            <span className={styles.badgeLabel}>Specialization:</span>
            {formatSpecialization(filters.specialization)}
            <IconX size={12} className={styles.badgeIcon} stroke={2} />
          </span>
        </Badge>
      );
    }
    
    if (filters.status && filters.status !== 'all') {
      activeFilters.push(
        <Badge key="status" colorScheme="info" variant="subtle" size="sm">
          <span className={styles.badgeContent}>
            <span className={styles.badgeLabel}>Status:</span>
            {filters.status.replace('_', ' ')}
            <IconX size={12} className={styles.badgeIcon} stroke={2} />
          </span>
        </Badge>
      );
    }
    
    if (filters.minSkill && filters.minSkill > 0) {
      activeFilters.push(
        <Badge key="minSkill" colorScheme="info" variant="subtle" size="sm">
          <span className={styles.badgeContent}>
            <span className={styles.badgeLabel}>Min Skill:</span>
            {filters.minSkill}
            <IconX size={12} className={styles.badgeIcon} stroke={2} />
          </span>
        </Badge>
      );
    }
    
    if (filters.maxCost) {
      activeFilters.push(
        <Badge key="maxCost" colorScheme="info" variant="subtle" size="sm">
          <span className={styles.badgeContent}>
            <span className={styles.badgeLabel}>Max Cost:</span>
            {filters.maxCost}
            <IconX size={12} className={styles.badgeIcon} stroke={2} />
          </span>
        </Badge>
      );
    }
    
    if (activeFilters.length === 0) return null;
    
    return (
      <div className={styles.activeFilters}>
        <span className={styles.activeFiltersLabel}>Active Filters:</span>
        <div className={styles.filterBadges}>
          {activeFilters}
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleFilterClear}
            leftIcon={<IconRefresh size={14} stroke={1.5} />}
          >
            Reset
          </Button>
        </div>
      </div>
    );
  };

  // Get max skill value for a specialist
  const getMaxSkill = (specialist: Specialist): number => {
    return Math.max(...Object.values(specialist.skills));
  };

  // Get average skill value for a specialist
  const getAverageSkill = (specialist: Specialist): number => {
    const skills = Object.values(specialist.skills);
    return Math.round(skills.reduce((a, b) => a + b, 0) / skills.length);
  };

  // Get top skill for a specialist
  const getTopSkill = (specialist: Specialist): { name: string; value: number } => {
    const skills = Object.entries(specialist.skills);
    const topSkill = skills.reduce((max, current) => {
      return current[1] > max[1] ? current : max;
    }, skills[0]);
    
    return {
      name: formatSpecialization(topSkill[0]),
      value: topSkill[1]
    };
  };

  if (loading) {
    return (
      <Card className={clsx(styles.container, className)} variant="default">
        <CardBody>
          <div className={styles.loadingContainer}>
            <IconLoader className={styles.loadingSpinner} size={32} stroke={1.5} />
            <p className={styles.loadingText}>Loading specialists...</p>
          </div>
        </CardBody>
      </Card>
    );
  }
  
  return (
    <Card className={clsx(styles.container, className)} variant="default">
      <CardHeader title={title} />
      <CardBody>
        {showFilters && (
          <div className={styles.filtersSection}>
            <div className={styles.searchWrapper}>
              <Input
                placeholder="Search specialists..."
                value={filters.search || ''}
                onChange={handleSearchChange}
                leftIcon={<IconSearch size={18} stroke={1.5} />}
                size="md"
                isFullWidth
              />
            </div>
            
            <div className={styles.filterActions}>
              <Button 
                variant="secondary" 
                leftIcon={<IconFilter size={18} stroke={1.5} />}
                rightIcon={<IconChevronDown size={16} stroke={1.5} className={clsx(styles.dropdownIcon, { [styles.dropdownIconOpen]: showFilterPanel })} />}
                size="sm"
                onClick={toggleFilterPanel}
              >
                Filter
              </Button>
              <Button 
                variant="ghost" 
                leftIcon={<IconSortAscending size={18} stroke={1.5} />}
                size="sm"
              >
                Sort
              </Button>
            </div>
            
            {showFilterPanel && (
              <div className={styles.filterPanel}>
                <div className={styles.filterPanelSection}>
                  <h4 className={styles.filterPanelTitle}>Status</h4>
                  <div className={styles.filterPanelOptions}>
                    {['all', 'available', 'hired', 'on_mission', 'training', 'unavailable'].map((status) => (
                      <div 
                        key={status} 
                        className={clsx(styles.filterOption, {
                          [styles.filterOptionActive]: filters.status === status
                        })}
                        onClick={() => onFilterChange && onFilterChange({...filters, status: status as SpecialistStatus | 'all'})}
                      >
                        {filters.status === status && (
                          <IconCircleCheck size={16} className={styles.filterOptionIcon} stroke={1.5} />
                        )}
                        <span className={styles.filterOptionLabel}>
                          {status === 'all' ? 'All Statuses' : formatSpecialization(status)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {renderActiveFilters()}
          </div>
        )}
        
        {!specialists || specialists.length === 0 ? (
          <div className={styles.emptyContainer}>
            <IconUsers className={styles.emptyIcon} size={48} stroke={1} />
            <p className={styles.emptyMessage}>{emptyMessage}</p>
            <Button 
              variant="secondary" 
              size="sm"
              leftIcon={<IconRefresh size={16} stroke={1.5} />}
              onClick={handleFilterClear}
            >
              Reset Filters
            </Button>
          </div>
        ) : (
          <div className={styles.specialistList}>
            {specialists.map(specialist => {
              const statusDetails = getStatusDetails(specialist.status);
              const topSkill = getTopSkill(specialist);
              
              return (
                <div 
                  key={specialist.id} 
                  className={styles.specialistCard}
                  onClick={() => onSpecialistSelect(specialist.id)}
                >
                  <div className={styles.specialistAvatar}>
                    {specialist.avatar ? (
                      <img src={specialist.avatar} alt={specialist.name} className={styles.avatarImage} />
                    ) : (
                      <IconUser size={24} stroke={1.5} />
                    )}
                  </div>
                  
                  <div className={styles.specialistInfo}>
                    <div className={styles.specialistHeader}>
                      <h3 className={styles.specialistName}>{specialist.name}</h3>
                      <Badge colorScheme={statusDetails.color} variant="subtle" size="sm">
                        {statusDetails.label}
                      </Badge>
                    </div>
                    
                    <div className={styles.specialistSubheader}>
                      <span className={styles.specializationLabel}>
                        {formatSpecialization(specialist.specialization)}
                      </span>
                      <span className={styles.levelBadge}>Level {specialist.level}</span>
                    </div>
                    
                    <div className={styles.specialistStats}>
                      <div className={styles.statItem}>
                        <IconChartBar size={14} className={styles.statIcon} stroke={1.5} />
                        <span className={styles.statLabel}>Top Skill:</span>
                        <span className={styles.statValue}>{topSkill.name} ({topSkill.value})</span>
                      </div>
                      
                      <div className={styles.statItem}>
                        <IconCoin size={14} className={styles.statIcon} stroke={1.5} />
                        <span className={styles.statLabel}>Cost:</span>
                        <span className={styles.statValue}>{specialist.cost} credits</span>
                      </div>
                    </div>
                  </div>
                  
                  <Button 
                    className={styles.viewDetailsButton}
                    variant="ghost" 
                    size="sm"
                    leftIcon={<IconEye size={16} stroke={1.5} />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onSpecialistSelect(specialist.id);
                    }}
                  >
                    Details
                  </Button>
                </div>
              );
            })}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default SpecialistList;