import React, { useEffect, useState } from 'react';
import { useNotificationsStore, useNotificationsSelectors } from '../../../features/notifications/model/store';
import { Notification } from '../../../features/notifications/model/types';
import styles from './NotificationList.module.css';

interface NotificationItemProps {
  notification: Notification;
  onDismiss: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onDismiss }) => {
  const [exiting, setExiting] = useState(false);
  
  useEffect(() => {
    // Set up auto-dismiss if specified
    if (notification.autoDismiss) {
      const timeout = setTimeout(() => {
        setExiting(true);
        
        // Give time for exit animation before removing
        setTimeout(() => {
          onDismiss(notification.id);
        }, 300);
      }, notification.dismissAfter || 5000);
      
      return () => clearTimeout(timeout);
    }
  }, [notification, onDismiss]);
  
  const handleDismiss = () => {
    setExiting(true);
    setTimeout(() => {
      onDismiss(notification.id);
    }, 300);
  };
  
  return (
    <div className={`${styles.notification} ${styles[notification.type]} ${exiting ? styles.exiting : ''}`}>
      <div className={styles.header}>
        <h3 className={styles.title}>{notification.title}</h3>
        <button className={styles.closeButton} onClick={handleDismiss}>
          ×
        </button>
      </div>
      <p className={styles.message}>{notification.message}</p>
      
      {notification.actions && notification.actions.length > 0 && (
        <div className={styles.actions}>
          {notification.actions.map((action, index) => (
            <button 
              key={index}
              className={styles.actionButton}
              onClick={() => {
                // Here you would handle action execution
                // For now, just dismiss the notification
                handleDismiss();
              }}
            >
              {action.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

const NotificationList: React.FC = () => {
  const removeNotification = useNotificationsStore(state => state.removeNotification);
  const { notifications } = useNotificationsSelectors();

  const handleDismiss = (id: string) => {
    removeNotification(id);
  };
  
  return (
    <div className={styles.notificationList}>
      {notifications.map(notification => (
        <NotificationItem 
          key={notification.id}
          notification={notification}
          onDismiss={handleDismiss}
        />
      ))}
    </div>
  );
};

export default NotificationList;
