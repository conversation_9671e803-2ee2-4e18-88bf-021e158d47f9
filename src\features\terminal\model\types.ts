// Terminal feature types

export interface TerminalCommand {
  commandId: string;
  command: string;
  instanceId: string;
  timestamp: number;
}

export interface CommandOutput {
  commandId: string;
  output: string;
  status: 'success' | 'error' | 'info' | 'warning';
  instanceId: string;
  timestamp: number;
}

export interface TerminalInstance {
  history: string[];
  currentDirectory: string;
  commandIndex: number;
}

export interface TerminalState {
  commands: TerminalCommand[];
  outputs: CommandOutput[];
  instances: Record<string, TerminalInstance>;
}
