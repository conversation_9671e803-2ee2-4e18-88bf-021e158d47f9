.contentContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.tabContent {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background-color: var(--bg-primary);
}

/* Tab visibility */
.tabPane {
  display: none;
  overflow: auto;
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-sm);
  transition: all 0.3s ease;
  backdrop-filter: blur(3px);
  position: relative;
}

.tabPane::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(rgba(32, 34, 60, 0.02), rgba(32, 34, 60, 0.05));
  z-index: 1;
}

.activeTab {
  display: block;
}

/* Single tab layout */
.single .tabPane.fullTab {
  width: 100%;
  height: 100%;
}

/* Split layout (two tabs side by side) */
.split .tabPane.leftSplitTab,
.split .tabPane.rightSplitTab {
  width: 50%;
  height: 100%;
  display: block;
}

.split .tabPane.leftSplitTab {
  border-right: 1px solid var(--border-color);
}

/* Triple layout (main tab + two stacked tabs) */
.triple .tabPane.mainTripleTab {
  width: 60%;
  height: 100%;
  display: block;
  border-right: 1px solid var(--border-color);
}

.triple .tabPane.topStackedTab,
.triple .tabPane.bottomStackedTab {
  width: 40%;
  height: 50%;
  display: block;
}

.triple .tabPane.topStackedTab {
  border-bottom: 1px solid var(--border-color);
}

/* Grid layout (2x2 grid) */
.grid .tabPane.topLeftGridTab,
.grid .tabPane.topRightGridTab,
.grid .tabPane.bottomLeftGridTab,
.grid .tabPane.bottomRightGridTab {
  width: 50%;
  height: 50%;
  display: block;
}

.grid .tabPane.topLeftGridTab {
  border-right: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.grid .tabPane.topRightGridTab {
  border-bottom: 1px solid var(--border-color);
}

.grid .tabPane.bottomLeftGridTab {
  border-right: 1px solid var(--border-color);
}

/* Scrollbar styling */
.tabPane::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.tabPane::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.tabPane::-webkit-scrollbar-thumb {
  background: var(--neon-cyan-muted);
  border-radius: 4px;
}

.tabPane::-webkit-scrollbar-thumb:hover {
  background: var(--neon-cyan);
}

/* Tab transition effects */
.tabPane {
  animation: fadeIn 0.2s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0.5; }
  to { opacity: 1; }
}
