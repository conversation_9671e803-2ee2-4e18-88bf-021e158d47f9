/**
 * Zustand-backed repository implementation for specialists
 */

import { useSpecialistStore } from '../model/store';
import { SpecialistRepository } from '@/core/specialist/interfaces';
import { Specialist } from '@/core/specialist/types';

/**
 * Zustand-backed implementation of the specialist repository
 * This connects the core domain layer to the Zustand state management
 */
export class ZustandSpecialistRepository implements SpecialistRepository {
  async getAvailableSpecialists(): Promise<Specialist[]> {
    // Get the store instance
    const store = useSpecialistStore.getState();
    
    // Fetch specialists if needed
    await store.fetchSpecialists('available');
    
    // Return available specialists
    const updatedStore = useSpecialistStore.getState();
    return updatedStore.availableSpecialists.map(id => updatedStore.specialists[id]).filter(Boolean);
  }
  
  async getHiredSpecialists(): Promise<Specialist[]> {
    // Get the store instance
    const store = useSpecialistStore.getState();
    
    // Fetch specialists if needed
    await store.fetchSpecialists('hired');
    
    // Return hired specialists
    const updatedStore = useSpecialistStore.getState();
    return updatedStore.hiredSpecialists.map(id => updatedStore.specialists[id]).filter(Boolean);
  }
  
  async getSpecialistById(id: string): Promise<Specialist | null> {
    // Get the current state
    const store = useSpecialistStore.getState();
    let specialist = store.specialists[id];
    
    // If specialist not found, try fetching all specialists
    if (!specialist) {
      await store.fetchSpecialists('all');
      const updatedStore = useSpecialistStore.getState();
      specialist = updatedStore.specialists[id];
    }
    
    return specialist || null;
  }
  
  async hireSpecialist(id: string): Promise<Specialist> {
    const store = useSpecialistStore.getState();
    
    try {
      await store.hireSpecialist(id);
      const updatedStore = useSpecialistStore.getState();
      const specialist = updatedStore.specialists[id];
      
      if (!specialist) {
        throw new Error('Failed to hire specialist');
      }
      
      return specialist;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to hire specialist');
    }
  }
  
  async fireSpecialist(id: string): Promise<boolean> {
    const store = useSpecialistStore.getState();
    
    try {
      await store.fireSpecialist(id);
      return true;
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Failed to fire specialist');
    }
  }
  
  async updateSpecialist(id: string, updates: Partial<Specialist>): Promise<Specialist> {
    // In a real implementation, we would dispatch an update action
    // For now, we'll throw an error since we don't have that action yet
    throw new Error('Not implemented yet');
  }
  
  async generateSpecialists(count: number): Promise<Specialist[]> {
    // In a real implementation, we would dispatch an action to generate specialists
    // For now, we'll throw an error since we don't have that action yet
    throw new Error('Not implemented yet');
  }
}
