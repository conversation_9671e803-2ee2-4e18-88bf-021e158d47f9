# Progression System Architecture

## Overview

The Progression System in CyberClash manages player advancement, skill development, reputation, and resource economy. This system provides long-term goals and rewards that motivate gameplay. Like other systems, it follows the Core Layer architecture pattern.

## Core Components

### Data Models

The progression system is built around these key types (partially defined in existing user types):

- **PlayerProfile**: Core player data including level, experience, and resources
- **SkillTree**: Structure defining available skills and their relationships
- **Reputation**: Standing with various factions affecting gameplay
- **Achievements**: Special accomplishments that unlock rewards
- **Resource**: Various currencies and assets used in the game

### Progression Service

The ProgressionService (to be implemented) will handle:

- Experience and level calculation
- Skill unlocking and advancement
- Reputation management
- Achievement tracking
- Resource economy balancing

### Repository

The UserRepository interface defines methods for data access:

- Managing player profiles
- Updating skills and abilities
- Tracking reputation changes
- Recording achievements
- Handling resource transactions

## Progression Mechanics

### Experience and Levels

Players advance through:

- Mission completion experience
- Skill usage bonuses
- Achievement rewards
- Special events

### Skill System

Players develop various skills:

- Technical skills (hacking, cryptography)
- Operational skills (OPSEC, social engineering)
- Management skills (team leadership, resource allocation)
- Specialized domain knowledge

### Reputation System

Relationships with factions affect gameplay:

- Access to faction-specific missions
- Market prices and availability
- Specialist recruitment options
- Narrative paths and outcomes

### Resource Economy

Players manage multiple resources:

- Credits (main currency)
- Data (technical resource)
- Intelligence (information assets)
- Access tokens (entry points)
- Special equipment

## Feature Implementation Plan

### State Management

Progression state is managed through Redux with:

- User slice
- Async thunks for API operations
- Selectors for data access

Key state includes:
- Player profile
- Skills and abilities
- Reputation standings
- Resource inventories

### UI Components

The progression UI will consist of:

1. **ProfileDashboard**: Overview of player status
2. **SkillTree**: Visual representation of skill development
3. **ReputationPanel**: Interface for managing faction relationships
4. **AchievementTracker**: Display of completed and available achievements
5. **ResourceManager**: Tool for monitoring and allocating resources

## Integration Points

### Mission Integration

Progression connects to missions through:

- Mission completion rewards
- Skill checks during missions
- Reputation effects from mission outcomes
- Resource requirements and rewards

### Team Integration

Progression affects team management:

- Skill-based specialist synergies
- Team development pathways
- Reputation-based recruitment options
- Resource allocation for team improvement

### Market Integration

Progression impacts market operations:

- Skill-based price negotiations
- Reputation-based market access
- Resource trading opportunities
- Unlockable market items

### Terminal Integration

Progression enhances terminal capabilities:

- Skill-based command effectiveness
- Unlockable terminal tools
- Resource-powered capabilities
- Achievement-based special commands

## Implementation Approach

The progression system will be implemented in these stages:

1. **Core Models**: Expand existing user types
2. **Service Layer**: Implement progression service
3. **Repository**: Enhance user repository
4. **State Management**: Extend user slice
5. **Basic UI**: Implement profile dashboard
6. **Detailed UI**: Add skill tree and reputation interfaces
7. **Integration**: Connect with other game systems

## Next Steps

To begin implementation:

1. Review and expand user types in `src/entities/user/model/types.ts`
2. Define progression service interface
3. Enhance user repository with progression-related methods
4. Extend the user Redux slice with progression features
