import { Command, CommandResult, CommandContext } from '../types';

const echoCommand: Command = {
  help: 'Displays the specified text.\nUsage: echo [text]',
  
  execute(args: string[], context: CommandContext): CommandResult {
    // Handle environment variable expansion
    const text = args.join(' ').replace(/\$([A-Za-z0-9_]+)/g, (match, varName) => {
      return context.environmentVariables?.[varName] || match;
    });
    
    return { 
      output: text, 
      status: 'success' 
    };
  }
};

export { echoCommand };
