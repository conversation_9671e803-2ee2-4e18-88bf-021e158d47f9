/**
 * Process terminal commands using the Core TerminalService
 */
import { CommandResult } from '@/core/terminal/types';
import { terminalService } from '@/core/terminal';

/**
 * Process a command string and return the result
 */
export async function processCommand(
  commandString: string, 
  currentDir: string,
  instanceId: string = 'default'
): Promise<CommandResult> {
  // Use the core terminal service to process the command
  return terminalService.executeCommand(commandString, instanceId);
}
