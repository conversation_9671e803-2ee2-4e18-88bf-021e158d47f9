.notificationList {
  position: fixed;
  top: var(--header-height);
  right: var(--space-md);
  width: 320px;
  max-height: calc(100vh - var(--header-height) - var(--footer-height) - var(--space-md));
  overflow-y: auto;
  z-index: var(--z-notification);
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  pointer-events: none;
}

.notification {
  background-color: var(--bg-secondary);
  border-left: 4px solid;
  border-radius: var(--border-radius-md);
  padding: var(--space-sm) var(--space-md);
  box-shadow: var(--shadow-md);
  opacity: 0.95;
  transform: translateX(100%);
  animation: slideIn 0.3s forwards;
  pointer-events: auto;
}

.notification.exiting {
  animation: slideOut 0.3s forwards;
}

.success {
  border-left-color: var(--color-success);
}

.success .typeIcon {
  color: var(--color-success);
}

.error {
  border-left-color: var(--color-error);
}

.error .typeIcon {
  color: var(--color-error);
}

.warning {
  border-left-color: var(--color-warning);
}

.warning .typeIcon {
  color: var(--color-warning);
}

.info {
  border-left-color: var(--color-info);
}

.info .typeIcon {
  color: var(--color-info);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-xs);
}

.titleWrapper {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.title {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.closeButton {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.closeButton:hover {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.1);
}

.message {
  margin: 0;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  padding-left: calc(16px + var(--space-xs)); /* Align with icon */
}

.actions {
  display: flex;
  gap: var(--space-sm);
  margin-top: var(--space-sm);
  padding-left: calc(16px + var(--space-xs)); /* Align with icon */
}

.actionButton {
  background: none;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--font-size-xs);
  color: var(--text-accent);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.actionButton:hover {
  background-color: var(--bg-hover);
  border-color: var(--border-active);
  box-shadow: var(--shadow-sm);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 0.95;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 0.95;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
