# CyberClash

A cybersecurity-themed strategy game with FSD architecture and Core Domain Layer.

## Architecture

This project uses Feature-Sliced Design (FSD) with an additional Core Layer for game logic:

```
src/
├── app/             # Application setup, providers
├── pages/           # Route-based page components 
├── widgets/         # Composite UI components
├── features/        # User interactions, state management
├── entities/        # Business domain models
├── core/            # Game logic (domain layer)
└── shared/          # Shared utilities, UI kit, types
```

## Core Layer

The Core Layer contains pure business logic with no UI or state management dependencies, making it portable and testable.

## Getting Started

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## Deployment

### Deploying to Vercel

The project is configured for easy deployment to Vercel:

1. Fork or clone this repository
2. Import the project into Vercel
3. Vercel will automatically detect the configuration and set up the deployment
4. Each push to the main branch will trigger a new deployment

### Continuous Integration

The project includes a GitHub Actions workflow for CI:

- Runs on push to main and on pull requests
- Builds the project to verify it compiles successfully
- Runs linting to check code quality

## Features

- Terminal system with command execution
- Mission system with objectives and rewards
- Team management with specialists and skills
- Notification system for game feedback
- Tab-based OS-like interface
