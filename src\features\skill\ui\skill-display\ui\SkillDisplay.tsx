import React from 'react';
import { <PERSON>, CardHeader, CardBody } from '@/shared/ui';
import { SkillType } from '@/core/specialist/types';
import styles from './SkillDisplay.module.css';
import {
  IconChartRadar,
  IconCode,
  IconBrain,
  IconUsers,
  IconBuildingBank,
  IconEye,
  IconLock
} from '@tabler/icons-react';
import clsx from 'clsx';

export interface SkillDisplayProps {
  skills: Record<SkillType, number>;
  title?: string;
  compact?: boolean;
  showIcons?: boolean;
  className?: string;
  maxLevel?: number;
  hideEmpty?: boolean;
}

const SkillDisplay: React.FC<SkillDisplayProps> = ({
  skills,
  title = 'Skills',
  compact = false,
  showIcons = true,
  className,
  maxLevel = 100,
  hideEmpty = false
}) => {
  // Format a skill string for display
  const formatSkillName = (skill: string): string => {
    return skill.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Get icon for a skill
  const getSkillIcon = (skill: string) => {
    // Technical skills
    if (skill.includes('hacking') || skill.includes('exploitation')) {
      return <IconCode size={16} stroke={1.5} />;
    }
    if (skill.includes('crypto') || skill.includes('malware') || skill.includes('development')) {
      return <IconBrain size={16} stroke={1.5} />;
    }
    if (skill.includes('network') || skill.includes('infiltration')) {
      return <IconLock size={16} stroke={1.5} />;
    }
    
    // Social skills
    if (skill.includes('social') || skill.includes('persuasion') || skill.includes('deception')) {
      return <IconUsers size={16} stroke={1.5} />;
    }
    
    // Intelligence skills
    if (skill.includes('intel') || skill.includes('osint') || skill.includes('threat')) {
      return <IconEye size={16} stroke={1.5} />;
    }
    
    // Physical skills
    if (skill.includes('physical') || skill.includes('surveillance') || skill.includes('counter')) {
      return <IconBuildingBank size={16} stroke={1.5} />;
    }
    
    // Default icon
    return <IconChartRadar size={16} stroke={1.5} />;
  };

  // Get skill category
  const getSkillCategory = (skill: string): string => {
    if (skill.includes('hacking') || skill.includes('crypto') || skill.includes('malware') || 
        skill.includes('network') || skill.includes('exploitation')) {
      return 'technical';
    }
    
    if (skill.includes('social') || skill.includes('persuasion') || skill.includes('deception')) {
      return 'social';
    }
    
    if (skill.includes('intel') || skill.includes('osint') || skill.includes('threat')) {
      return 'intelligence';
    }
    
    if (skill.includes('physical') || skill.includes('surveillance') || skill.includes('counter')) {
      return 'physical';
    }
    
    return 'other';
  };

  // Group skills by category
  const groupedSkills: Record<string, Array<[SkillType, number]>> = {};
  
  Object.entries(skills).forEach(([skill, value]) => {
    if (hideEmpty && value === 0) return;
    
    const category = getSkillCategory(skill);
    if (!groupedSkills[category]) {
      groupedSkills[category] = [];
    }
    groupedSkills[category].push([skill as SkillType, value]);
  });
  
  // Sort skills by value (descending) within each category
  Object.keys(groupedSkills).forEach(category => {
    groupedSkills[category].sort((a, b) => b[1] - a[1]);
  });

  // Generate a gradient class based on skill level
  const getGradientClass = (value: number): string => {
    const percentage = (value / maxLevel) * 100;
    if (percentage >= 80) return styles.expertLevel;
    if (percentage >= 60) return styles.advancedLevel;
    if (percentage >= 40) return styles.intermediateLevel;
    if (percentage >= 20) return styles.basicLevel;
    return styles.noviceLevel;
  };

  // Get category title
  const getCategoryTitle = (category: string): string => {
    switch (category) {
      case 'technical': return 'Technical Skills';
      case 'social': return 'Social Skills';
      case 'intelligence': return 'Intelligence Skills';
      case 'physical': return 'Physical Skills';
      default: return 'Other Skills';
    }
  };

  // Render compact version
  if (compact) {
    return (
      <Card 
        className={clsx(styles.skillDisplayCompact, className)}
        variant="default"
      >
        {title && <CardHeader title={title} />}
        <CardBody>
          <div className={styles.skillListCompact}>
            {Object.entries(skills)
              .filter(([_, value]) => !hideEmpty || value > 0)
              .sort(([_, a], [__, b]) => b - a)
              .slice(0, 5) // Only show top 5 skills in compact mode
              .map(([skill, value]) => (
                <div key={skill} className={styles.skillItemCompact}>
                  {showIcons && (
                    <div className={styles.skillIconCompact}>
                      {getSkillIcon(skill)}
                    </div>
                  )}
                  <div className={styles.skillInfoCompact}>
                    <div className={styles.skillNameCompact}>
                      {formatSkillName(skill)}
                    </div>
                    <div className={styles.skillBarContainerCompact}>
                      <div 
                        className={clsx(styles.skillBarCompact, getGradientClass(value))}
                        style={{ width: `${(value / maxLevel) * 100}%` }}
                      />
                    </div>
                  </div>
                  <div className={styles.skillValueCompact}>{value}</div>
                </div>
              ))
            }
          </div>
        </CardBody>
      </Card>
    );
  }

  // Render full version grouped by category
  return (
    <Card 
      className={clsx(styles.skillDisplay, className)}
      variant="default"
    >
      {title && <CardHeader title={title} />}
      <CardBody>
        {Object.keys(groupedSkills).length === 0 ? (
          <div className={styles.emptyState}>
            <IconChartRadar size={32} className={styles.emptyIcon} stroke={1.5} />
            <p className={styles.emptyText}>No skills to display</p>
          </div>
        ) : (
          Object.entries(groupedSkills).map(([category, skillList]) => (
            <div key={category} className={styles.skillCategory}>
              <h3 className={styles.categoryTitle}>{getCategoryTitle(category)}</h3>
              <div className={styles.skillList}>
                {skillList.map(([skill, value]) => (
                  <div key={skill} className={styles.skillItem}>
                    <div className={styles.skillHeader}>
                      {showIcons && (
                        <div className={styles.skillIcon}>
                          {getSkillIcon(skill)}
                        </div>
                      )}
                      <div className={styles.skillName}>
                        {formatSkillName(skill)}
                      </div>
                      <div className={styles.skillValue}>{value}</div>
                    </div>
                    <div className={styles.skillBarContainer}>
                      <div 
                        className={clsx(styles.skillBar, getGradientClass(value))}
                        style={{ width: `${(value / maxLevel) * 100}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
      </CardBody>
    </Card>
  );
};

export default SkillDisplay;