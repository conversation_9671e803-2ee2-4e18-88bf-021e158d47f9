// This file is deprecated - migrating to Zustand stores
// Individual stores are now located in their respective feature directories

// Re-export store hooks for backward compatibility during migration
export { useTabsStore } from '../../features/tabs/model/store';
export { useTerminalStore } from '../../features/terminal/model/store';
export { useMissionStore } from '../../features/mission/model/store';
export { useSpecialistStore } from '../../features/specialist/model/store';
export { useNotificationsStore } from '../../features/notifications/model/store';
