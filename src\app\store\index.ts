import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';

// Import reducers
import tabsReducer from '../../features/tabs/model/slice';
import terminalReducer from '../../features/terminal/model/slice';
import missionReducer from '../../features/mission/model/slice';
import specialistReducer from '../../features/specialist/model/slice';
import notificationsReducer from '../../features/notifications/model/slice';

export const store = configureStore({
  reducer: {
    tabs: tabsReducer,
    terminal: terminalReducer,
    mission: missionReducer,
    specialist: specialistReducer,
    notifications: notificationsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore non-serializable action paths
        ignoredActionPaths: ['payload.actions', 'meta.arg.actions'],
        ignoredPaths: [
          'notifications.notifications.*.actions',
        ],
      },
    }),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
