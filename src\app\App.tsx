import React, { useEffect } from 'react';
import AppShell from '../widgets/app-shell/ui/AppShell';
import { useTabsStore } from '../features/tabs/model/store';
import './styles/index.css';

const App: React.FC = () => {
  const addTab = useTabsStore(state => state.addTab);

  useEffect(() => {
    // Add a terminal tab for development/testing
    addTab({
      id: 'terminal-default',
      title: 'Terminal',
      closable: true,
      type: 'terminal',
    });

    // Add a mission tab for development/testing
    addTab({
      id: 'mission-default',
      title: 'Missions',
      closable: true,
      type: 'mission',
    });

    // Add a team tab for development/testing
    addTab({
      id: 'team-default',
      title: 'Team',
      closable: true,
      type: 'team',
    });
  }, [addTab]);

  return <AppShell />;
};

export default App;
