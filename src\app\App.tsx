import React, { useEffect } from 'react';
import { Provider } from 'react-redux';
import { store } from './store';
import AppShell from '../widgets/app-shell/ui/AppShell';
import { addTab } from '../features/tabs/model/slice';
import './styles/index.css';

const App: React.FC = () => {
  useEffect(() => {
    // Add a terminal tab for development/testing
    store.dispatch(addTab({
      id: 'terminal-default',
      title: 'Terminal',
      closable: true,
      type: 'terminal',
    }));
    
    // Add a mission tab for development/testing
    store.dispatch(addTab({
      id: 'mission-default',
      title: 'Missions',
      closable: true,
      type: 'mission',
    }));
    
    // Add a team tab for development/testing
    store.dispatch(addTab({
      id: 'team-default',
      title: 'Team',
      closable: true,
      type: 'team',
    }));
  }, []);
  
  return (
    <Provider store={store}>
      <AppShell />
    </Provider>
  );
};

export default App;
