.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-radius: var(--border-radius-sm);
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Badge variants */
.variant-solid {
  color: var(--cyberpunk-black);
  border: none;
}

.variant-outline {
  background-color: transparent;
  border: 1px solid;
}

.variant-subtle {
  background-color: transparent;
  border: none;
}

/* Badge color schemes */
.variant-solid.colorScheme-primary {
  background-color: var(--neon-cyan);
  box-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
}

.variant-outline.colorScheme-primary {
  color: var(--neon-cyan);
  border-color: var(--neon-cyan);
}

.variant-subtle.colorScheme-primary {
  color: var(--neon-cyan);
  background-color: var(--neon-cyan-muted);
}

.variant-solid.colorScheme-secondary {
  background-color: var(--neon-purple);
  box-shadow: 0 0 5px rgba(158, 0, 255, 0.5);
}

.variant-outline.colorScheme-secondary {
  color: var(--neon-purple);
  border-color: var(--neon-purple);
}

.variant-subtle.colorScheme-secondary {
  color: var(--neon-purple);
  background-color: var(--neon-purple-muted);
}

.variant-solid.colorScheme-success {
  background-color: var(--neon-green);
  box-shadow: 0 0 5px rgba(0, 255, 159, 0.5);
}

.variant-outline.colorScheme-success {
  color: var(--neon-green);
  border-color: var(--neon-green);
}

.variant-subtle.colorScheme-success {
  color: var(--neon-green);
  background-color: var(--neon-green-muted);
}

.variant-solid.colorScheme-danger {
  background-color: var(--neon-pink);
  box-shadow: 0 0 5px rgba(255, 0, 103, 0.5);
}

.variant-outline.colorScheme-danger {
  color: var(--neon-pink);
  border-color: var(--neon-pink);
}

.variant-subtle.colorScheme-danger {
  color: var(--neon-pink);
  background-color: var(--neon-pink-muted);
}

.variant-solid.colorScheme-warning {
  background-color: var(--neon-yellow);
  box-shadow: 0 0 5px rgba(255, 223, 0, 0.5);
}

.variant-outline.colorScheme-warning {
  color: var(--neon-yellow);
  border-color: var(--neon-yellow);
}

.variant-subtle.colorScheme-warning {
  color: var(--neon-yellow);
  background-color: var(--neon-yellow-muted);
}

.variant-solid.colorScheme-info {
  background-color: var(--neon-blue);
  box-shadow: 0 0 5px rgba(0, 136, 255, 0.5);
}

.variant-outline.colorScheme-info {
  color: var(--neon-blue);
  border-color: var(--neon-blue);
}

.variant-subtle.colorScheme-info {
  color: var(--neon-blue);
  background-color: rgba(0, 136, 255, 0.2);
}

.variant-solid.colorScheme-gray {
  background-color: var(--text-secondary);
  box-shadow: none;
}

.variant-outline.colorScheme-gray {
  color: var(--text-secondary);
  border-color: var(--text-secondary);
}

.variant-subtle.colorScheme-gray {
  color: var(--text-secondary);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Badge sizes */
.size-sm {
  font-size: var(--font-size-xs);
  padding: 0.15em 0.4em;
}

.size-md {
  font-size: var(--font-size-xs);
  padding: 0.2em 0.5em;
}

.size-lg {
  font-size: var(--font-size-sm);
  padding: 0.25em 0.6em;
}

/* Rounded badge */
.rounded {
  border-radius: 9999px;
}

/* Clickable badge */
.clickable {
  cursor: pointer;
  transition: all var(--transition-fast);
}

.clickable:hover {
  transform: translateY(-1px);
  filter: brightness(1.1);
}

.clickable:active {
  transform: translateY(0);
}

/* Icons */
.leftIcon {
  margin-right: 0.25em;
  display: inline-flex;
  align-items: center;
}

.rightIcon {
  margin-left: 0.25em;
  display: inline-flex;
  align-items: center;
}

/* Animation for solid badge */
.variant-solid {
  position: relative;
  overflow: hidden;
}

.variant-solid::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 1s ease;
}

.variant-solid:hover::after {
  left: 100%;
}
