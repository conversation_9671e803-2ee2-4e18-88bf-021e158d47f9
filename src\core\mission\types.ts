// Core domain types for Mission functionality

export type MissionStatus = 'available' | 'in_progress' | 'completed' | 'failed';
export type MissionDifficulty = 'novice' | 'professional' | 'expert' | 'elite' | 'legendary';
export type ObjectiveStatus = 'pending' | 'in_progress' | 'completed' | 'failed';
export type MissionType = 'hack' | 'data_extraction' | 'intelligence' | 'defense' | 'forensic';
export type MissionCategory = 'offensive' | 'defensive' | 'research';

export interface Objective {
  id: string;
  title: string;
  description: string;
  status: ObjectiveStatus;
  required: boolean;
  dependsOn?: string[]; // IDs of objectives that must be completed first
  reward?: Reward;
  completionSteps?: CompletionStep[];
  currentStep?: number;
}

export interface CompletionStep {
  id: string;
  description: string;
  requiresUserAction: boolean;
  actionType?: 'terminal_command' | 'tool_use' | 'file_analysis' | 'choice';
  actionParams?: Record<string, any>;
  successCriteria?: Record<string, any>;
}

export interface Reward {
  credits?: number;
  resources?: Record<string, number>;
  reputation?: Record<string, number>;
  experience?: number;
  items?: string[];
  intel?: string[];
}

export interface Mission {
  id: string;
  title: string;
  description: string;
  brief: string;
  faction: string;
  type: MissionType;
  category: MissionCategory;
  objectives: Objective[];
  rewards: Reward;
  riskLevel: number; // 1-10 scale
  difficulty: MissionDifficulty;
  timeConstraints: TimeConstraint | null;
  requiredSpecializations: string[];
  requiredResources: RequiredResource[];
  status: MissionStatus;
  startedAt?: string;
  completedAt?: string;
  failedAt?: string;
  expiresAt?: string;
  location?: string;
  targetSystem?: string;
}

export interface TimeConstraint {
  totalTime: number; // In minutes or game time units
  remaining: number;
  critical: number; // Threshold for critical warning
}

export interface RequiredResource {
  id: string;
  name: string;
  quantity: number;
}

export interface MissionProgress {
  missionId: string;
  currentObjective: string | null;
  completedObjectives: string[];
  failedObjectives: string[];
  timeElapsed: number;
  logs: MissionLog[];
}

export interface MissionLog {
  timestamp: number;
  type: 'info' | 'success' | 'warning' | 'error';
  message: string;
  objectiveId?: string;
}

export interface MissionFilter {
  category?: MissionCategory;
  difficulty?: MissionDifficulty;
  faction?: string;
  type?: MissionType;
  status?: MissionStatus;
}

export interface MissionSuccess {
  missionId: string;
  rewards: Reward;
  completionTime: number;
  bonusRewards?: Reward;
}

export interface MissionFailure {
  missionId: string;
  reason: string;
  consequences?: Record<string, any>;
  partialRewards?: Reward;
}
