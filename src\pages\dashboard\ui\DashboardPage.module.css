.dashboardPage {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.dashboardTitle {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  background: linear-gradient(90deg, #00F0FF, #0088FF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcomeMessage {
  font-size: 14px;
  color: #B0B0B0;
}

.dashboardContent {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboardGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto auto;
  gap: 24px;
}

.sectionTitle {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #E0E0E0;
}

/* Quick Actions */
.quickActions {
  margin-bottom: 8px;
}

.actionButtons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.actionButton {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #2A2A2A;
  border: 1px solid #333333;
  border-radius: 8px;
  color: #E0E0E0;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.actionButton:hover {
  background-color: #333333;
  border-color: #00F0FF;
}

.buttonIcon {
  margin-right: 8px;
  font-size: 16px;
}

/* Active Missions */
.activeMissions {
  grid-column: 1;
  grid-row: 1;
}

.missionsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.missionCard {
  padding: 16px;
  background-color: #2A2A2A;
  border: 1px solid #333333;
  border-radius: 8px;
}

.missionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.missionTitle {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.missionDifficulty {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.novice {
  background-color: rgba(52, 199, 89, 0.2);
  color: #34C759;
}

.professional {
  background-color: rgba(0, 122, 255, 0.2);
  color: #007AFF;
}

.expert {
  background-color: rgba(255, 149, 0, 0.2);
  color: #FF9500;
}

.elite, .legendary {
  background-color: rgba(255, 45, 85, 0.2);
  color: #FF2D55;
}

.missionDetails {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.missionFaction {
  font-size: 14px;
  color: #B0B0B0;
}

.progressContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progressLabel {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #B0B0B0;
}

.progressBar {
  height: 6px;
  background-color: #333333;
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background-color: #00F0FF;
  border-radius: 3px;
}

/* Recent Events */
.recentEvents {
  grid-column: 2;
  grid-row: 1;
}

.eventsList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.eventItem {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #2A2A2A;
  border-left: 3px solid transparent;
  border-radius: 4px;
}

.eventItem.info {
  border-left-color: #007AFF;
}

.eventItem.success {
  border-left-color: #34C759;
}

.eventItem.warning {
  border-left-color: #FF9500;
}

.eventItem.error {
  border-left-color: #FF2D55;
}

.eventTime {
  font-size: 12px;
  color: #B0B0B0;
  margin-right: 12px;
  min-width: 60px;
}

.eventTitle {
  font-size: 14px;
}

/* Resource Summary */
.resourceSummary {
  grid-column: 1;
  grid-row: 2;
}

.resourceGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.resourceCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background-color: #2A2A2A;
  border: 1px solid #333333;
  border-radius: 8px;
}

.resourceIcon {
  font-size: 24px;
  margin-bottom: 8px;
}

.resourceName {
  font-size: 14px;
  color: #B0B0B0;
  margin-bottom: 4px;
}

.resourceValue {
  font-size: 18px;
  font-weight: 600;
}

/* Faction Standings */
.factionStandings {
  grid-column: 2;
  grid-row: 2;
}

.standingsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.standingItem {
  display: flex;
  align-items: center;
  gap: 12px;
}

.factionName {
  flex: 0 0 150px;
  font-size: 14px;
}

.standingBar {
  flex: 1;
  height: 8px;
  background-color: #333333;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.standingFill {
  height: 100%;
  border-radius: 4px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.standingFill.positive {
  background-color: #34C759;
  transform: translateX(0);
  left: 0;
}

.standingFill.negative {
  background-color: #FF3B30;
  transform: translateX(-100%);
  left: 50%;
}

.standingValue {
  flex: 0 0 40px;
  text-align: right;
  font-weight: 500;
}

.emptyState {
  padding: 24px;
  text-align: center;
  background-color: #2A2A2A;
  border-radius: 8px;
  color: #707070;
  font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboardGrid {
    grid-template-columns: 1fr;
  }
  
  .activeMissions,
  .recentEvents,
  .resourceSummary,
  .factionStandings {
    grid-column: 1;
  }
  
  .activeMissions {
    grid-row: 1;
  }
  
  .recentEvents {
    grid-row: 2;
  }
  
  .resourceSummary {
    grid-row: 3;
  }
  
  .factionStandings {
    grid-row: 4;
  }
}
