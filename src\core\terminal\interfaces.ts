import { CommandResult } from './types';

/**
 * Interface for the Terminal Service
 * This allows different implementations (e.g., local, remote, mock)
 */
export interface ITerminalService {
  /**
   * Execute a command string in a specific terminal instance
   */
  executeCommand(commandString: string, instanceId: string): CommandResult;
  
  /**
   * Get a list of all available command names
   */
  getAvailableCommands(): string[];
  
  /**
   * Get help text for a specific command
   */
  getCommandHelp(commandName: string): string;
}
