import {
  Mission,
  MissionFilter,
  MissionSuccess,
  MissionFailure,
  MissionProgress,
  Objective,
  MissionStatus,
  ObjectiveStatus,
  MissionLog
} from './types';
import { IMissionService } from './interfaces';
import { MissionRepository } from '../common/repositories/interfaces';

export class MissionService implements IMissionService {
  constructor(private missionRepository: MissionRepository) {}

  /**
   * Get all available missions with optional filtering
   */
  async getAvailableMissions(filter?: MissionFilter): Promise<Mission[]> {
    const missions = await this.missionRepository.getAvailableMissions();
    
    if (!filter) return missions;
    
    // Apply filters if provided
    return missions.filter(mission => {
      if (filter.category && mission.category !== filter.category) return false;
      if (filter.difficulty && mission.difficulty !== filter.difficulty) return false;
      if (filter.faction && mission.faction !== filter.faction) return false;
      if (filter.type && mission.type !== filter.type) return false;
      if (filter.status && mission.status !== filter.status) return false;
      return true;
    });
  }
  
  /**
   * Get a specific mission by ID
   */
  async getMissionById(id: string): Promise<Mission | null> {
    return this.missionRepository.getMissionById(id);
  }
  
  /**
   * Start a mission
   */
  async startMission(missionId: string): Promise<MissionProgress> {
    // Get the mission
    const mission = await this.getMissionById(missionId);
    if (!mission) throw new Error(`Mission with id ${missionId} not found`);
    
    // Check if mission can be started
    if (mission.status !== 'available') {
      throw new Error(`Mission ${mission.title} cannot be started. Current status: ${mission.status}`);
    }
    
    // Check if mission has expired
    if (mission.expiresAt && new Date(mission.expiresAt) < new Date()) {
      throw new Error(`Mission ${mission.title} has expired`);
    }
    
    // Update mission status
    await this.missionRepository.updateMissionStatus(missionId, 'in_progress');
    
    // Initialize mission progress
    const firstObjective = this.getNextObjective(mission);
    const progress: MissionProgress = {
      missionId,
      currentObjective: firstObjective?.id || null,
      completedObjectives: [],
      failedObjectives: [],
      timeElapsed: 0,
      logs: [
        {
          timestamp: Date.now(),
          type: 'info',
          message: `Mission "${mission.title}" started.`
        }
      ]
    };
    
    // Save initial progress
    await this.missionRepository.saveMissionProgress(missionId, progress);
    
    return progress;
  }
  
  /**
   * Complete an objective in a mission
   */
  async completeObjective(missionId: string, objectiveId: string): Promise<MissionProgress> {
    // Get the mission and current progress
    const mission = await this.getMissionById(missionId);
    if (!mission) throw new Error(`Mission with id ${missionId} not found`);
    
    if (mission.status !== 'in_progress') {
      throw new Error(`Cannot complete objective: mission is not in progress`);
    }
    
    // Find the objective
    const objective = mission.objectives.find(obj => obj.id === objectiveId);
    if (!objective) throw new Error(`Objective with id ${objectiveId} not found in mission ${missionId}`);
    
    // Check if dependencies are met
    if (!this.areObjectiveDependenciesMet(mission, objectiveId)) {
      throw new Error(`Objective dependencies are not met for ${objective.title}`);
    }
    
    // Get current progress
    const progress = await this.missionRepository.getMissionProgress(missionId);
    if (!progress) throw new Error(`Progress not found for mission ${missionId}`);
    
    // Update objective status
    await this.missionRepository.updateObjectiveStatus(missionId, objectiveId, 'completed');
    
    // Update progress
    const updatedProgress: MissionProgress = {
      ...progress,
      completedObjectives: [...progress.completedObjectives, objectiveId],
      logs: [...progress.logs, {
        timestamp: Date.now(),
        type: 'success',
        message: `Objective "${objective.title}" completed.`,
        objectiveId
      }]
    };
    
    // Find next objective
    const nextObjective = this.getNextObjective({
      ...mission,
      objectives: mission.objectives.map(obj => 
        obj.id === objectiveId 
          ? { ...obj, status: 'completed' as ObjectiveStatus } 
          : obj
      )
    });
    
    updatedProgress.currentObjective = nextObjective?.id || null;
    
    // Check if mission is complete
    if (this.isMissionComplete({
      ...mission,
      objectives: mission.objectives.map(obj => 
        obj.id === objectiveId || updatedProgress.completedObjectives.includes(obj.id)
          ? { ...obj, status: 'completed' as ObjectiveStatus } 
          : obj
      )
    })) {
      await this.completeMission(missionId);
    } else {
      // Save updated progress
      await this.missionRepository.saveMissionProgress(missionId, updatedProgress);
    }
    
    return updatedProgress;
  }
  
  /**
   * Fail an objective in a mission
   */
  async failObjective(missionId: string, objectiveId: string, reason: string): Promise<MissionProgress> {
    // Get the mission and current progress
    const mission = await this.getMissionById(missionId);
    if (!mission) throw new Error(`Mission with id ${missionId} not found`);
    
    if (mission.status !== 'in_progress') {
      throw new Error(`Cannot fail objective: mission is not in progress`);
    }
    
    // Find the objective
    const objective = mission.objectives.find(obj => obj.id === objectiveId);
    if (!objective) throw new Error(`Objective with id ${objectiveId} not found in mission ${missionId}`);
    
    // Get current progress
    const progress = await this.missionRepository.getMissionProgress(missionId);
    if (!progress) throw new Error(`Progress not found for mission ${missionId}`);
    
    // Update objective status
    await this.missionRepository.updateObjectiveStatus(missionId, objectiveId, 'failed');
    
    // Update progress
    const updatedProgress: MissionProgress = {
      ...progress,
      failedObjectives: [...progress.failedObjectives, objectiveId],
      logs: [...progress.logs, {
        timestamp: Date.now(),
        type: 'error',
        message: `Objective "${objective.title}" failed: ${reason}`,
        objectiveId
      }]
    };
    
    // If this was a required objective, fail the mission
    if (objective.required) {
      await this.failMission(missionId, `Required objective "${objective.title}" failed: ${reason}`);
    } else {
      // Find next objective
      const nextObjective = this.getNextObjective({
        ...mission,
        objectives: mission.objectives.map(obj => 
          obj.id === objectiveId 
            ? { ...obj, status: 'failed' as ObjectiveStatus } 
            : obj
        )
      });
      
      updatedProgress.currentObjective = nextObjective?.id || null;
      
      // Save updated progress
      await this.missionRepository.saveMissionProgress(missionId, updatedProgress);
    }
    
    return updatedProgress;
  }
  
  /**
   * Update mission progress
   */
  async updateMissionProgress(missionId: string, progressUpdates: Partial<MissionProgress>): Promise<MissionProgress> {
    // Get current progress
    const currentProgress = await this.missionRepository.getMissionProgress(missionId);
    if (!currentProgress) throw new Error(`Progress not found for mission ${missionId}`);
    
    // Merge updates with current progress
    const updatedProgress: MissionProgress = {
      ...currentProgress,
      ...progressUpdates,
      // Always preserve mission ID
      missionId,
      // Merge logs if provided, otherwise keep current logs
      logs: progressUpdates.logs 
        ? [...currentProgress.logs, ...progressUpdates.logs]
        : currentProgress.logs
    };
    
    // Save updated progress
    await this.missionRepository.saveMissionProgress(missionId, updatedProgress);
    
    return updatedProgress;
  }
  
  /**
   * Complete a mission
   */
  async completeMission(missionId: string): Promise<MissionSuccess> {
    // Get the mission
    const mission = await this.getMissionById(missionId);
    if (!mission) throw new Error(`Mission with id ${missionId} not found`);
    
    // Get current progress
    const progress = await this.missionRepository.getMissionProgress(missionId);
    if (!progress) throw new Error(`Progress not found for mission ${missionId}`);
    
    // Update mission status
    await this.missionRepository.updateMissionStatus(missionId, 'completed');
    
    // Calculate rewards (could have bonus based on time, etc.)
    const rewards = mission.rewards;
    
    // Record completion timestamp
    const completionTime = Date.now();
    await this.missionRepository.setMissionCompletionTime(missionId, completionTime);
    
    // Add final log
    const updatedProgress: MissionProgress = {
      ...progress,
      currentObjective: null,
      logs: [...progress.logs, {
        timestamp: completionTime,
        type: 'success',
        message: `Mission "${mission.title}" completed successfully.`
      }]
    };
    
    // Save final progress
    await this.missionRepository.saveMissionProgress(missionId, updatedProgress);
    
    // Return success result
    return {
      missionId,
      rewards,
      completionTime: progress.timeElapsed
    };
  }
  
  /**
   * Fail a mission
   */
  async failMission(missionId: string, reason: string): Promise<MissionFailure> {
    // Get the mission
    const mission = await this.getMissionById(missionId);
    if (!mission) throw new Error(`Mission with id ${missionId} not found`);
    
    // Get current progress
    const progress = await this.missionRepository.getMissionProgress(missionId);
    if (!progress) throw new Error(`Progress not found for mission ${missionId}`);
    
    // Update mission status
    await this.missionRepository.updateMissionStatus(missionId, 'failed');
    
    // Record failure timestamp
    const failureTime = Date.now();
    await this.missionRepository.setMissionFailureTime(missionId, failureTime);
    
    // Add final log
    const updatedProgress: MissionProgress = {
      ...progress,
      currentObjective: null,
      logs: [...progress.logs, {
        timestamp: failureTime,
        type: 'error',
        message: `Mission "${mission.title}" failed: ${reason}`
      }]
    };
    
    // Save final progress
    await this.missionRepository.saveMissionProgress(missionId, updatedProgress);
    
    // Return failure result (with optional partial rewards for completed objectives)
    return {
      missionId,
      reason,
      consequences: {} // Could implement reputation loss, resource loss, etc.
    };
  }
  
  /**
   * Check if an objective's dependencies are met
   */
  areObjectiveDependenciesMet(mission: Mission, objectiveId: string): boolean {
    const objective = mission.objectives.find(obj => obj.id === objectiveId);
    if (!objective) return false;
    
    // If no dependencies, always met
    if (!objective.dependsOn || objective.dependsOn.length === 0) return true;
    
    // Check that all dependencies are completed
    return objective.dependsOn.every(depId => {
      const dependency = mission.objectives.find(obj => obj.id === depId);
      return dependency && dependency.status === 'completed';
    });
  }
  
  /**
   * Get the next available objective in a mission
   */
  getNextObjective(mission: Mission): Objective | null {
    // Find the first uncompleted and unlocked objective
    const nextObjective = mission.objectives.find(obj => 
      (obj.status === 'pending' || obj.status === 'in_progress') && 
      this.areObjectiveDependenciesMet(mission, obj.id)
    );
    
    return nextObjective || null;
  }
  
  /**
   * Check if a mission is complete (all required objectives done)
   */
  isMissionComplete(mission: Mission): boolean {
    // Check if all required objectives are completed
    const allRequiredCompleted = mission.objectives
      .filter(obj => obj.required)
      .every(obj => obj.status === 'completed');
    
    return allRequiredCompleted;
  }
  
  /**
   * Generate a new mission (for dynamic mission generation)
   */
  async generateMission(params: Record<string, any>): Promise<Mission> {
    // This would be where we'd implement procedural mission generation
    // For now, let's return a simple placeholder mission
    const mission: Mission = {
      id: `generated-${Date.now()}`,
      title: params.title || 'Generated Mission',
      description: params.description || 'A generated mission',
      brief: params.brief || 'This mission was dynamically generated.',
      faction: params.faction || 'Independent',
      type: params.type || 'hack',
      category: params.category || 'offensive',
      objectives: [
        {
          id: `obj-1-${Date.now()}`,
          title: 'Primary Objective',
          description: 'Complete the main task',
          status: 'pending',
          required: true
        },
        {
          id: `obj-2-${Date.now()}`,
          title: 'Secondary Objective',
          description: 'An optional bonus objective',
          status: 'pending',
          required: false,
          dependsOn: [`obj-1-${Date.now()}`]
        }
      ],
      rewards: {
        credits: params.credits || 1000,
        experience: params.experience || 100
      },
      riskLevel: params.riskLevel || 5,
      difficulty: params.difficulty || 'professional',
      timeConstraints: null,
      requiredSpecializations: [],
      requiredResources: [],
      status: 'available'
    };
    
    // Save the generated mission
    await this.missionRepository.saveMission(mission);
    
    return mission;
  }
}
