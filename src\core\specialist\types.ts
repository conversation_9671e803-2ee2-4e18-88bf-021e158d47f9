/**
 * Core types for the specialist/team system
 */

// Specialist status types
export type SpecialistStatus = 'available' | 'hired' | 'unavailable' | 'on_mission' | 'training';

// Specialist skill types
export type SkillType = 
  // Technical skills
  | 'hacking' 
  | 'cryptography' 
  | 'malware_development' 
  | 'network_infiltration'
  | 'exploitation'
  // Social skills
  | 'social_engineering'
  | 'persuasion'
  | 'deception'
  // Intelligence skills
  | 'intel_analysis'
  | 'osint'
  | 'threat_hunting'
  // Physical skills
  | 'physical_security'
  | 'surveillance'
  | 'counter_surveillance';

// Specialist trait types
export type TraitType =
  // Positive traits
  | 'meticulous' // Reduces chance of detection
  | 'innovative' // Occasionally provides creative solutions
  | 'connected' // Has special contacts
  | 'loyal' // Resistant to compromise
  | 'efficient' // Completes tasks faster
  | 'calm' // Better under pressure
  // Negative traits
  | 'impatient' // Increases risk of detection
  | 'careless' // Makes mistakes
  | 'greedy' // Demands higher payment
  | 'unstable' // May fail under pressure
  | 'suspicious' // May be under surveillance
  | 'unreliable'; // May not complete tasks

// Specialist type/role
export type SpecializationType =
  // Technical specialists 
  | 'malware_developer'
  | 'network_infiltrator'
  | 'exploit_researcher'
  | 'system_administrator'
  | 'tool_developer'
  // Field operatives
  | 'social_engineer'
  | 'physical_security_specialist'
  | 'surveillance_expert'
  | 'asset_handler'
  | 'extraction_specialist'
  // Intelligence personnel
  | 'osint_analyst'
  | 'data_miner'
  | 'crypto_specialist'
  | 'threat_researcher'
  | 'profiler';

// Specialist trait definition
export interface Trait {
  type: TraitType;
  name: string;
  description: string;
  effect: string;
  isPositive: boolean;
}

// Specialist data model
export interface Specialist {
  id: string;
  name: string;
  avatar?: string;
  specialization: SpecializationType;
  skills: Record<SkillType, number>; // 0-100 skill levels
  traits: Trait[];
  attributes: {
    reliability: number; // 0-100
    discretion: number; // 0-100
    efficiency: number; // 0-100
    loyalty: number; // 0-100
  };
  background: string;
  cost: number;
  status: SpecialistStatus;
  level: number;
  experience: number;
  hireDate?: string;
  history?: MissionHistory[];
}

// Mission history for specialist
export interface MissionHistory {
  missionId: string;
  missionName: string;
  date: string;
  outcome: 'success' | 'failure' | 'partial';
  notes?: string;
}

// Team configuration for a mission
export interface Team {
  id: string;
  name: string;
  members: TeamMember[];
  createdAt: string;
  lastMission?: string;
}

// Team member role
export interface TeamMember {
  specialistId: string;
  role: 'leader' | 'member' | 'support';
  assignedTasks?: string[];
}

// Training program
export interface TrainingProgram {
  id: string;
  name: string;
  description: string;
  duration: number; // in days
  targetSkills: Array<{ skill: SkillType, improvement: number }>;
  cost: number;
  requiredLevel?: number;
}
