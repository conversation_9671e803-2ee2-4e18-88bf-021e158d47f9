import { 
  Mission, 
  MissionProgress, 
  MissionStatus, 
  MissionFilter,
  MissionSuccess,
  MissionFailure
} from '@/core/mission/types';

export interface MissionState {
  // All missions indexed by ID
  missions: Record<string, Mission>;
  
  // Lists of mission IDs by status
  availableMissions: string[];
  inProgressMissions: string[];
  completedMissions: string[];
  failedMissions: string[];
  
  // Currently active/viewed mission
  activeMissionId: string | null;
  
  // Mission progress tracking
  missionProgress: Record<string, MissionProgress>;
  
  // Loading states
  isLoading: boolean;
  error: string | null;
}

// Note: Payload types removed - Zustand actions use direct parameters
