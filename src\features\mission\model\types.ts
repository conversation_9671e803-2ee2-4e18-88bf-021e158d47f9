import { 
  Mission, 
  MissionProgress, 
  MissionStatus, 
  MissionFilter,
  MissionSuccess,
  MissionFailure
} from '@/core/mission/types';

export interface MissionState {
  // All missions indexed by ID
  missions: Record<string, Mission>;
  
  // Lists of mission IDs by status
  availableMissions: string[];
  inProgressMissions: string[];
  completedMissions: string[];
  failedMissions: string[];
  
  // Currently active/viewed mission
  activeMissionId: string | null;
  
  // Mission progress tracking
  missionProgress: Record<string, MissionProgress>;
  
  // Loading states
  isLoading: boolean;
  error: string | null;
}

// Mission action payload types
export interface FetchMissionsPayload {
  filter?: MissionFilter;
}

export interface MissionStartedPayload {
  missionId: string;
  progress: MissionProgress;
}

export interface MissionProgressUpdatedPayload {
  missionId: string;
  progress: MissionProgress;
}

export interface MissionCompletedPayload {
  missionId: string;
  result: MissionSuccess;
}

export interface MissionFailedPayload {
  missionId: string;
  result: MissionFailure;
}

export interface ObjectiveCompletedPayload {
  missionId: string;
  objectiveId: string;
  progress: MissionProgress;
}

export interface ObjectiveFailedPayload {
  missionId: string;
  objectiveId: string;
  reason: string;
  progress: MissionProgress;
}
