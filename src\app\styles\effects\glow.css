/* Glow effects */

/* Text glow */
.glow-text {
  color: var(--neon-cyan);
  text-shadow: 0 0 5px rgba(0, 240, 255, 0.5), 0 0 10px rgba(0, 240, 255, 0.3);
}

.glow-text-pink {
  color: var(--neon-pink);
  text-shadow: 0 0 5px rgba(255, 0, 103, 0.5), 0 0 10px rgba(255, 0, 103, 0.3);
}

.glow-text-purple {
  color: var(--neon-purple);
  text-shadow: 0 0 5px rgba(158, 0, 255, 0.5), 0 0 10px rgba(158, 0, 255, 0.3);
}

.glow-text-yellow {
  color: var(--neon-yellow);
  text-shadow: 0 0 5px rgba(255, 223, 0, 0.5), 0 0 10px rgba(255, 223, 0, 0.3);
}

/* Animated glow */
@keyframes glow {
  0% {
    text-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(0, 240, 255, 0.8), 0 0 30px rgba(0, 240, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
  }
}

.glow-text-animate {
  animation: glow 2s ease-in-out infinite;
}

/* Border glow */
.border-glow {
  border: 1px solid var(--border-active);
  box-shadow: var(--shadow-neon);
}

.border-glow-pink {
  border: 1px solid var(--neon-pink);
  box-shadow: var(--shadow-pink);
}

.border-glow-purple {
  border: 1px solid var(--neon-purple);
  box-shadow: var(--shadow-purple);
}

.border-glow-yellow {
  border: 1px solid var(--neon-yellow);
  box-shadow: var(--shadow-yellow);
}

/* Animated border glow */
@keyframes border-pulse {
  0%, 100% {
    border-color: rgba(0, 240, 255, 0.3);
    box-shadow: 0 0 5px rgba(0, 240, 255, 0.1);
  }
  50% {
    border-color: rgba(0, 240, 255, 0.8);
    box-shadow: 0 0 15px rgba(0, 240, 255, 0.3);
  }
}

.border-pulse {
  border: 1px solid rgba(0, 240, 255, 0.3);
  animation: border-pulse 2s ease-in-out infinite;
}

/* Box glow */
.box-glow {
  position: relative;
}

.box-glow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  border-radius: inherit;
  box-shadow: 0 0 20px rgba(0, 240, 255, 0.4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.box-glow:hover::after {
  opacity: 1;
}
