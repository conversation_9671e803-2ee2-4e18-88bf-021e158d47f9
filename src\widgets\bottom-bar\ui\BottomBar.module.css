.bottomBar {
  display: flex;
  justify-content: center; /* Changed from space-between to center */
  align-items: center;
  height: var(--footer-height);
  background-color: var(--bg-elevated);
  border-top: 1px solid var(--border-color);
  padding: 0 var(--space-md);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  z-index: var(--z-bottombar);
}

.appLauncher {
  display: flex;
  gap: var(--space-md);
  align-items: center;
  justify-content: center; /* Added to center the app buttons */
  width: 100%; /* Take full width */
}

.appButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: var(--space-xs);
  padding: var(--space-xs) var(--space-sm);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  position: relative;
  transition: all var(--transition-normal);
  border-radius: var(--border-radius-md);
}

.appButton::before {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 100%;
  height: 2px;
  background: var(--gradient-primary);
  transition: transform var(--transition-normal);
}

.appButton:hover {
  color: var(--text-primary);
  background-color: rgba(0, 240, 255, 0.05);
  box-shadow: 0 0 8px rgba(0, 240, 255, 0.2);
}

.appButton.active {
  color: var(--text-accent);
  text-shadow: 0 0 8px rgba(0, 240, 255, 0.5);
}

.appButton.active::before {
  transform: translateX(-50%) scaleX(1);
}

.iconWrapper {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-normal);
}

.iconWrapper.active {
  color: var(--text-accent);
  background-color: rgba(0, 240, 255, 0.1);
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.3);
}

.appName {
  font-size: var(--font-size-xs);
  letter-spacing: 0.5px;
  text-transform: uppercase;
  white-space: nowrap;
}

/* Removed systemInfo styles as they're no longer needed */
