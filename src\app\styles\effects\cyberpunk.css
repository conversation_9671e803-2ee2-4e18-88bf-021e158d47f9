/* Cyberpunk-specific visual effects */

/* Circuit board background pattern */
.circuit-bg {
  position: relative;
}

.circuit-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 10 H 90 V 90 H 10 L 10 10' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M30 10 V 30 H 10' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M70 10 V 30 H 90' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M30 90 V 70 H 10' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M70 90 V 70 H 90' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M10 50 H 30' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M90 50 H 70' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M50 10 V 30' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M50 90 V 70' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Ccircle cx='50' cy='50' r='5' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3C/svg%3E");
  opacity: 0.2;
  z-index: -1;
  animation: circuitPulse 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes circuitPulse {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
}

/* Holographic effect */
.holographic {
  position: relative;
  background: linear-gradient(135deg, rgba(0, 240, 255, 0.1), rgba(158, 0, 255, 0.1));
  border: 1px solid rgba(0, 240, 255, 0.3);
  border-radius: var(--border-radius-md);
  backdrop-filter: blur(5px);
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.3);
}

.holographic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 25%, 
    rgba(0, 240, 255, 0.1) 25%, 
    rgba(0, 240, 255, 0.1) 50%, 
    transparent 50%, 
    transparent 75%, 
    rgba(0, 240, 255, 0.1) 75%
  );
  background-size: 8px 8px;
  pointer-events: none;
  opacity: 0.3;
  z-index: 1;
  animation: hologram 10s linear infinite alternate;
}

@keyframes hologram {
  0% {
    opacity: 0.1;
    background-position: 0% 0%;
    filter: hue-rotate(0deg);
  }
  50% {
    opacity: 0.3;
    filter: hue-rotate(180deg);
  }
  100% {
    opacity: 0.1;
    background-position: 100% 100%;
    filter: hue-rotate(360deg);
  }
}

/* Data flow effect */
.data-flow {
  position: relative;
  overflow: hidden;
}

.data-flow::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    var(--bg-secondary) 0%, 
    var(--bg-secondary) 45%, 
    rgba(0, 240, 255, 0.2) 50%,
    var(--bg-secondary) 55%, 
    var(--bg-secondary) 100%);
  background-size: 200% 100%;
  animation: dataFlow 2s linear infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes dataFlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

/* Matrix code rain effect */
.matrix-code {
  position: relative;
  color: var(--neon-green);
}

.matrix-code::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Ctext x='10' y='30' font-family='monospace' font-size='10' fill='%2300ff9f' fill-opacity='0.2'%3E01011%3C/text%3E%3Ctext x='50' y='20' font-family='monospace' font-size='10' fill='%2300ff9f' fill-opacity='0.2'%3E10110%3C/text%3E%3Ctext x='30' y='50' font-family='monospace' font-size='10' fill='%2300ff9f' fill-opacity='0.2'%3E11001%3C/text%3E%3Ctext x='70' y='70' font-family='monospace' font-size='10' fill='%2300ff9f' fill-opacity='0.2'%3E00101%3C/text%3E%3Ctext x='10' y='80' font-family='monospace' font-size='10' fill='%2300ff9f' fill-opacity='0.2'%3E11010%3C/text%3E%3C/svg%3E");
  pointer-events: none;
  z-index: 0;
  opacity: 0.15;
  animation: matrixRain 30s linear infinite;
}

@keyframes matrixRain {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 0% 100%;
  }
}

/* Retro terminal style */
.retro-terminal {
  background-color: var(--cyberpunk-black);
  color: var(--neon-green);
  font-family: var(--font-mono);
  border-radius: var(--border-radius-sm);
  padding: 1rem;
  border: 1px solid var(--neon-green);
}

.retro-terminal::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 255, 159, 0.05);
  pointer-events: none;
  z-index: 1;
}

/* Digital distortion effect */
@keyframes distort {
  0%, 100% {
    clip-path: none;
  }
  5% {
    clip-path: polygon(0 0, 100% 0, 100% 90%, 0 87%);
  }
  10% {
    clip-path: polygon(0 3%, 100% 0, 100% 100%, 0 97%);
  }
  15%, 95% {
    clip-path: none;
  }
}

.distortion {
  position: relative;
}

.distortion::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 240, 255, 0.2);
  opacity: 0;
  z-index: 10;
  pointer-events: none;
  animation: distort 12s linear infinite;
}

/* Broken screen effect */
.broken-screen {
  position: relative;
  overflow: hidden;
}

.broken-screen::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 10 L 90 90 M 90 10 L 10 90 M 30 10 L 90 70 M 10 30 L 70 90 M 10 50 L 50 90 M 50 10 L 90 50' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.2'/%3E%3C/svg%3E");
  z-index: 5;
  pointer-events: none;
  opacity: 0.1;
}

/* Biometric scan grid */
@keyframes bioScan {
  0% {
    background-position: 0% 0%;
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    background-position: 0% 100%;
    opacity: 0.1;
  }
}

.biometric-scan {
  position: relative;
}

.biometric-scan::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(transparent 95%, rgba(0, 240, 255, 0.3) 95%),
    linear-gradient(90deg, transparent 95%, rgba(0, 240, 255, 0.3) 95%);
  background-size: 20px 20px;
  pointer-events: none;
  opacity: 0.1;
  z-index: 2;
  animation: bioScan 10s infinite;
}

/* Cybernetic augmentation visual */
.augmentation-highlight {
  position: relative;
}

.augmentation-highlight::after {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: 
    linear-gradient(45deg, transparent 48%, var(--neon-cyan) 49%, var(--neon-cyan) 51%, transparent 52%) 0 0 / 20px 20px,
    linear-gradient(-45deg, transparent 48%, var(--neon-cyan) 49%, var(--neon-cyan) 51%, transparent 52%) 0 0 / 20px 20px;
  z-index: -1;
  border-radius: inherit;
  opacity: 0.3;
  pointer-events: none;
  animation: augmentPulse 3s infinite;
}

@keyframes augmentPulse {
  0%, 100% {
    opacity: 0.1;
  }
  50% {
    opacity: 0.3;
  }
}

/* Tech grid background */
.tech-grid {
  position: relative;
}

.tech-grid::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0, 240, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 240, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: -1;
  pointer-events: none;
}