import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  Notification, 
  NotificationType, 
  NotificationAction, 
  NotificationsState 
} from './types';

interface NotificationsActions {
  addNotification: (params: {
    type: NotificationType;
    title: string;
    message: string;
    autoDismiss?: boolean;
    dismissAfter?: number;
    actions?: NotificationAction[];
  }) => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
}

type NotificationsStore = NotificationsState & NotificationsActions;

const initialState: NotificationsState = {
  notifications: []
};

export const useNotificationsStore = create<NotificationsStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      addNotification: ({ type, title, message, autoDismiss = true, dismissAfter = 5000, actions }) => {
        const notification: Notification = {
          id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type,
          title,
          message,
          timestamp: Date.now(),
          autoDismiss,
          dismissAfter,
          actions
        };
        
        set((state) => ({
          notifications: [...state.notifications, notification]
        }));
        
        // Auto-dismiss if enabled
        if (autoDismiss && dismissAfter) {
          setTimeout(() => {
            get().removeNotification(notification.id);
          }, dismissAfter);
        }
      },
      
      removeNotification: (id: string) => {
        set((state) => ({
          notifications: state.notifications.filter(notification => notification.id !== id)
        }));
      },
      
      clearAllNotifications: () => {
        set({ notifications: [] });
      },
    }),
    {
      name: 'notifications-store',
    }
  )
);

// Selectors for easier access to derived state
export const useNotificationsSelectors = () => {
  const store = useNotificationsStore();
  
  return {
    notifications: store.notifications,
    notificationCount: store.notifications.length,
    hasNotifications: store.notifications.length > 0,
    
    // Helper functions
    getNotificationById: (id: string) => store.notifications.find(n => n.id === id) || null,
    getNotificationsByType: (type: NotificationType) => store.notifications.filter(n => n.type === type),
  };
};
