# CyberClash Architecture Guide

## Overview

CyberClash uses an extended Feature-Sliced Design (FSD) architecture with an additional Core Layer for game logic separation. This approach provides a clean separation of concerns, improved testability, and easier migration to a backend in the future.

## Architecture Layers

```
src/
├── app/             # Application setup, providers
├── pages/           # Route-based page components 
├── widgets/         # Composite UI components
├── features/        # User interactions, state management
├── entities/        # Business domain models
├── core/            # Game logic (domain layer)
└── shared/          # Shared utilities, UI kit, types
```

## Layers in Detail

### App Layer

Contains application initialization, routing, and styles. This layer bootstraps the application and manages global configuration.

**Key files:**
- `src/app/App.tsx` - Main application component
- `src/app/store/index.ts` - Store re-exports (deprecated, migrated to Zustand)
- `src/app/styles/global.css` - Global styles

### Pages Layer

Top-level route components that compose widgets and features for complete screens in the application.

**Example pages:**
- Dashboard
- Terminal
- Mission Control
- Marketplace
- Team Management

### Widgets Layer

Complex UI components combining multiple features that can be reused across different pages.

**Example widgets:**
- TabBar
- StatusBar
- NotificationPanel
- TerminalConsole
- MissionCard

### Features Layer

Business logic and user interactions tied to specific functionality in the game.

**Example features:**
- Terminal operations
- Mission management
- Team coordination
- Market transactions
- Notification system

### Entities Layer

Business domain models representing core concepts in the game.

**Example entities:**
- User
- Mission
- Specialist
- Tool
- Resource

### Core Layer (Extension to FSD)

Pure game logic with no UI or state management dependencies. This layer contains domain services, repositories, and types.

```
src/core/
├── terminal/
│   ├── interfaces.ts
│   ├── types.ts
│   ├── TerminalService.ts
│   ├── commands/
│   └── index.ts
├── mission/
│   ├── interfaces.ts
│   ├── types.ts
│   ├── MissionService.ts
│   └── MissionGenerator.ts
├── specialist/
│   ├── interfaces.ts
│   ├── types.ts
│   └── SpecialistService.ts
├── market/
│   ├── interfaces.ts
│   ├── types.ts
│   └── MarketService.ts
└── common/
    └── repositories/
        └── interfaces.ts
```

**Domains:**
- Terminal
- Mission
- Specialist
- Market

### Shared Layer

Utilities, types, and UI components used across all layers.

**Example contents:**
- UI components
- Type definitions
- Utility functions
- Constants

## Core Design Principles

1. **Domain-Driven Design**: Group functionality by business domain rather than technical concerns
2. **Pure Logic**: Core services contain only game logic with no UI or state management dependencies
3. **Interface-Based Design**: Use interfaces to define service and repository contracts
4. **Dependency Inversion**: Higher-level modules don't depend on implementation details
5. **Repository Pattern**: Abstract data access behind repository interfaces

## Key Components

### Domain Services

Services implement game logic for specific domains:

```typescript
export class TerminalService {
  constructor(private fileSystemRepo: FileSystemRepository) {}
  
  executeCommand(command: string, context: CommandContext): CommandResult {
    // Parse command
    // Execute appropriate logic
    // Return results 
  }
}
```

### Repositories

Repositories abstract data access and modification:

```typescript
export interface MissionRepository {
  getAvailableMissions(): Mission[];
  getMissionById(id: string): Mission | undefined;
  updateMissionStatus(id: string, status: MissionStatus): void;
}
```

### Domain Models & Types

Core types represent the essential game data models:

```typescript
export interface Specialist {
  id: string;
  name: string;
  skills: Record<SkillType, number>;
  traits: SpecialistTrait[];
  status: SpecialistStatus;
}

export type SkillType = 'hacking' | 'socialEngineering' | 'cryptography';
export type SpecialistStatus = 'available' | 'onMission' | 'training';
```

## Data Flow

1. **UI Events** trigger actions in the Features layer
2. Feature layer uses Core Services for game logic
3. Core Services use Repositories for data access
4. Repositories connect to Redux state or API
5. State changes propagate back through Components

Visually:

```
User Interaction → UI Component → Feature Action → Core Service → Repository → Store/API
        ↑                                                       ↓
        └────────────── State Update ──────────────←
```

## Integration with FSD Architecture

### Features Layer as Adapter

The features layer acts as an adapter between core services and UI:

```typescript
// features/mission/model/actions.ts
import { missionService } from '@/core/mission';

export const startMission = createAsyncThunk(
  'mission/start',
  async ({missionId, teamIds}: StartMissionParams, {getState}) => {
    // Get current state
    const state = getState();
    
    // Call core service
    return missionService.startMission(missionId, teamIds);
  }
);
```

### Repository Implementation in Features

Implement repositories in the features layer to connect core with Zustand stores:

```typescript
// features/mission/repository/ZustandMissionRepository.ts
import { MissionRepository } from '@/core/common/repositories';
import { useMissionStore } from '../model/store';

export class ZustandMissionRepository implements MissionRepository {
  getAvailableMissions() {
    const store = useMissionStore.getState();
    return store.availableMissions.map(id => store.missions[id]).filter(Boolean);
  }

  async updateMissionStatus(id: string, status: string) {
    const store = useMissionStore.getState();
    await store.updateMissionProgress(id, { status });
  }
}
```

## Service Initialization

Create a service initialization module to manage dependencies:

```typescript
// core/index.ts
import { TerminalService } from './terminal/TerminalService';
import { ReduxTerminalRepository } from '../features/terminal/repository';

// Create repositories
const terminalRepository = new ReduxTerminalRepository();

// Initialize services with dependencies
export const terminalService = new TerminalService(terminalRepository);
```

## Backend Migration Path

When migrating to a backend:

1. Move core modules to the server project (they have no frontend dependencies)
2. Implement API-based repositories in the frontend:

```typescript
// features/mission/repository/apiMissionRepository.ts
export class ApiMissionRepository implements MissionRepository {
  async getAvailableMissions() {
    const response = await fetch('/api/missions?status=available');
    return response.json();
  }
  
  async updateMissionStatus(id, status) {
    await fetch(`/api/missions/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
```

3. Inject the new repository implementations into services

## Benefits

1. **Testability**: Core logic is isolated and easily unit-tested
2. **Maintainability**: Clear separation of concerns
3. **Portability**: Game logic can be moved to a backend with minimal changes
4. **Consistency**: Game rules are centralized and not scattered across UI components
5. **Collaboration**: Different team members can work on UI and game logic independently
