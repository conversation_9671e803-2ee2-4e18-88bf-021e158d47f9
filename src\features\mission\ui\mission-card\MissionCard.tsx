import React from 'react';
import { Mission } from '@/core/mission/types';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ody, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from '@/shared/ui';
import styles from './MissionCard.module.css';
import {
  IconTargetArrow,
  IconUsers,
  IconShieldCheck,
  IconDatabase,
  IconSearch,
  IconTrophy,
  IconAlertTriangle,
  IconClock,
  IconBriefcase,
  IconBulb
} from '@tabler/icons-react';

interface MissionCardProps {
  mission: Mission;
  onClick?: () => void;
  compact?: boolean;
  showActions?: boolean;
  onDetailsClick?: () => void;
}

export const MissionCard: React.FC<MissionCardProps> = ({
  mission,
  onClick,
  compact = false,
  showActions = true,
  onDetailsClick
}) => {
  // Helper function to get badge color based on difficulty
  const getDifficultyColor = (difficulty: string): {
    colorScheme: 'success' | 'info' | 'warning' | 'danger' | 'secondary';
    label: string;
  } => {
    switch (difficulty) {
      case 'novice':
        return { colorScheme: 'success', label: 'Novice' };
      case 'professional':
        return { colorScheme: 'info', label: 'Professional' };
      case 'expert':
        return { colorScheme: 'warning', label: 'Expert' };
      case 'elite':
        return { colorScheme: 'danger', label: 'Elite' };
      case 'legendary':
        return { colorScheme: 'secondary', label: 'Legendary' };
      default:
        return { colorScheme: 'info', label: difficulty };
    }
  };

  // Get difficulty color and label
  const difficultyInfo = getDifficultyColor(mission.difficulty);
  
  // Format risk level to show as stars with appropriate color
  const formatRiskLevel = (level: number) => {
    return (
      <div className={styles.riskLevelContainer}>
        {Array.from({ length: 5 }).map((_, index) => (
          <span 
            key={index}
            className={`${styles.riskStar} ${index < level / 2 ? styles.filledStar : styles.emptyStar}`}
          >
            ★
          </span>
        ))}
        <span className={styles.riskValue}>{level}/10</span>
      </div>
    );
  };

  // Get mission type icon and label
  const getMissionTypeInfo = (type: string): { icon: React.ReactNode; label: string } => {
    switch (type) {
      case 'hack':
        return { 
          icon: <IconTargetArrow size={16} stroke={1.5} />, 
          label: 'System Breach' 
        };
      case 'data_extraction':
        return { 
          icon: <IconDatabase size={16} stroke={1.5} />, 
          label: 'Data Extraction' 
        };
      case 'intelligence':
        return { 
          icon: <IconUsers size={16} stroke={1.5} />, 
          label: 'Intelligence' 
        };
      case 'defense':
        return { 
          icon: <IconShieldCheck size={16} stroke={1.5} />, 
          label: 'Defense' 
        };
      case 'forensic':
        return { 
          icon: <IconSearch size={16} stroke={1.5} />, 
          label: 'Forensic Analysis' 
        };
      default:
        return { 
          icon: <IconTargetArrow size={16} stroke={1.5} />, 
          label: type.replace('_', ' ') 
        };
    }
  };

  // Get mission category icon and color
  const getCategoryInfo = (category: string): { 
    icon: React.ReactNode; 
    colorScheme: 'success' | 'info' | 'warning' | 'danger' | 'secondary'; 
  } => {
    switch (category) {
      case 'offensive':
        return { 
          icon: <IconTargetArrow size={16} stroke={1.5} />, 
          colorScheme: 'danger'
        };
      case 'defensive':
        return { 
          icon: <IconShieldCheck size={16} stroke={1.5} />, 
          colorScheme: 'success'
        };
      case 'research':
        return { 
          icon: <IconBulb size={16} stroke={1.5} />, 
          colorScheme: 'info'
        };
      default:
        return { 
          icon: <IconBriefcase size={16} stroke={1.5} />, 
          colorScheme: 'secondary'
        };
    }
  };

  const missionTypeInfo = getMissionTypeInfo(mission.type);
  const categoryInfo = getCategoryInfo(mission.category);

  // Function to determine if mission is expiring soon
  const isExpiringSoon = () => {
    if (!mission.expiresAt) return false;
    const expiryDate = new Date(mission.expiresAt);
    const now = new Date();
    const timeLeft = expiryDate.getTime() - now.getTime();
    return timeLeft > 0 && timeLeft < 24 * 60 * 60 * 1000; // Less than 24 hours
  };

  // Render compact version
  if (compact) {
    return (
      <Card 
        variant="default" 
        accent={difficultyInfo.colorScheme} 
        isClickable={!!onClick} 
        onClick={onClick}
        className={styles.compactCard}
      >
        <CardBody className={styles.compactCardBody}>
          <div className={styles.headerRow}>
            <h3 className={styles.compactTitle}>{mission.title}</h3>
            <Badge 
              colorScheme={difficultyInfo.colorScheme} 
              variant="subtle" 
              size="sm"
            >
              {difficultyInfo.label}
            </Badge>
          </div>
          <div className={styles.factionRow}>
            <Badge 
              colorScheme={categoryInfo.colorScheme} 
              variant="subtle" 
              size="sm"
              leftIcon={categoryInfo.icon}
            >
              {mission.category}
            </Badge>
            <Badge 
              colorScheme="gray" 
              variant="subtle" 
              size="sm"
              leftIcon={missionTypeInfo.icon}
            >
              {missionTypeInfo.label}
            </Badge>
          </div>
          {isExpiringSoon() && (
            <div className={styles.expiryWarning}>
              <IconClock size={14} stroke={1.5} />
              <span>Expires soon</span>
            </div>
          )}
        </CardBody>
      </Card>
    );
  }
  
  // Render full version
  return (
    <Card 
      variant="elevated" 
      accent={difficultyInfo.colorScheme} 
      isClickable={!!onClick && !showActions} 
      onClick={!showActions ? onClick : undefined}
      className={styles.fullCard}
    >
      <CardHeader 
        title={(
          <div className={styles.titleWrapper}>
            <span className={styles.missionTitle}>{mission.title}</span>
            <Badge 
              colorScheme={difficultyInfo.colorScheme} 
              variant="subtle" 
              size="md"
            >
              {difficultyInfo.label}
            </Badge>
          </div>
        )}
        subtitle={(
          <div className={styles.subtitleWrapper}>
            <span className={styles.factionName}>{mission.faction}</span>
            <div className={styles.badgeContainer}>
              <Badge 
                colorScheme={categoryInfo.colorScheme} 
                variant="subtle" 
                size="sm"
                leftIcon={categoryInfo.icon}
              >
                {mission.category}
              </Badge>
              <Badge 
                colorScheme="gray" 
                variant="subtle" 
                size="sm"
                leftIcon={missionTypeInfo.icon}
              >
                {missionTypeInfo.label}
              </Badge>
            </div>
          </div>
        )}
      />
      
      <CardBody>
        {isExpiringSoon() && (
          <div className={styles.expiryBanner}>
            <IconAlertTriangle size={16} stroke={1.5} />
            <span>This mission will expire soon</span>
          </div>
        )}

        <p className={styles.description}>{mission.description}</p>
        
        <div className={styles.details}>
          <div className={styles.detailRow}>
            <span className={styles.label}>Risk Level:</span>
            <span className={styles.value}>
              {formatRiskLevel(mission.riskLevel)}
            </span>
          </div>
          
          <div className={styles.detailRow}>
            <span className={styles.label}>Objectives:</span>
            <span className={styles.value}>
              {mission.objectives.length} task{mission.objectives.length !== 1 ? 's' : ''}
            </span>
          </div>
          
          {mission.timeConstraints && (
            <div className={styles.detailRow}>
              <span className={styles.label}>Time Limit:</span>
              <span className={styles.value}>
                <IconClock size={16} stroke={1.5} className={styles.inlineIcon} />
                {mission.timeConstraints.totalTime} minutes
              </span>
            </div>
          )}
          
          {mission.requiredSpecializations.length > 0 && (
            <div className={styles.detailRow}>
              <span className={styles.label}>Required Skills:</span>
              <div className={styles.skillsContainer}>
                {mission.requiredSpecializations.map(skill => (
                  <Badge key={skill} colorScheme="info" variant="outline" size="sm">
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <div className={styles.rewards}>
          <span className={styles.rewardsLabel}>
            <IconTrophy size={16} stroke={1.5} className={styles.rewardsIcon} />
            Rewards:
          </span>
          <div className={styles.rewardsList}>
            {mission.rewards.credits && (
              <Badge colorScheme="success" variant="subtle" size="sm" className={styles.rewardItem}>
                💰 {mission.rewards.credits} Credits
              </Badge>
            )}
            {mission.rewards.experience && (
              <Badge colorScheme="info" variant="subtle" size="sm" className={styles.rewardItem}>
                ⭐ {mission.rewards.experience} XP
              </Badge>
            )}
            {mission.rewards.reputation && Object.entries(mission.rewards.reputation).map(([faction, amount]) => (
              <Badge 
                key={faction} 
                colorScheme={amount > 0 ? 'success' : 'danger'} 
                variant="subtle" 
                size="sm"
                className={styles.rewardItem}
              >
                {amount > 0 ? '📈' : '📉'} {faction}: {amount > 0 ? '+' : ''}{amount}
              </Badge>
            ))}
            {mission.rewards.items && mission.rewards.items.length > 0 && (
              <Badge colorScheme="secondary" variant="subtle" size="sm" className={styles.rewardItem}>
                🔍 {mission.rewards.items.length} Item{mission.rewards.items.length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
        </div>
      </CardBody>
      
      {showActions && (
        <CardFooter align="right">
          <Button 
            variant="secondary" 
            size="sm" 
            onClick={onDetailsClick || onClick}
            leftIcon={<IconSearch size={16} stroke={1.5} />}
          >
            Details
          </Button>
          <Button 
            variant="primary" 
            size="sm" 
            onClick={onClick}
            leftIcon={<IconBriefcase size={16} stroke={1.5} />}
          >
            Accept
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};