/* Buttons */
button, .button {
  cursor: pointer;
  font-family: var(--font-main);
  font-size: 0.9rem;
  padding: 0.5em 1em;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

button::before, .button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 240, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

button:hover::before, .button:hover::before {
  left: 100%;
}

button:hover, .button:hover {
  border-color: var(--border-active);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

button:active, .button:active {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}

.button-primary {
  background: var(--neon-cyan);
  color: var(--cyberpunk-black);
  border: none;
  font-weight: bold;
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
}

.button-primary:hover {
  background: var(--neon-purple);
  box-shadow: 0 0 15px rgba(158, 0, 255, 0.6);
}

.button-danger {
  background: var(--neon-pink);
  color: var(--cyberpunk-black);
  border: none;
  font-weight: bold;
  box-shadow: 0 0 10px rgba(255, 0, 103, 0.5);
}

.button-danger:hover {
  background: var(--neon-pink);
  box-shadow: 0 0 15px rgba(255, 0, 103, 0.7);
}

/* Disabled state */
button:disabled,
.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled::before,
.button:disabled::before {
  display: none;
}

/* Button sizes */
.button-sm {
  font-size: 0.8rem;
  padding: 0.3em 0.8em;
}

.button-lg {
  font-size: 1rem;
  padding: 0.7em 1.2em;
}

/* Icon buttons */
.button-icon {
  padding: 0.5em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.button-icon svg {
  width: 1.2em;
  height: 1.2em;
}
