import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Badge } from '@/shared/ui';
import { Reward } from '@/core/mission/types';
import styles from './RewardDisplay.module.css';
import {
  IconCoin,
  IconStar,
  IconArrowUp,
  IconArrowDown,
  IconPackage,
  IconDatabase,
  IconInfoCircle,
  IconTrophy
} from '@tabler/icons-react';
import clsx from 'clsx';

export interface RewardDisplayProps {
  rewards: Reward;
  title?: string;
  compact?: boolean;
  showIcons?: boolean;
  className?: string;
  showLabels?: boolean;
}

const RewardDisplay: React.FC<RewardDisplayProps> = ({
  rewards,
  title = 'Rewards',
  compact = false,
  showIcons = true,
  className,
  showLabels = true
}) => {
  // Check if rewards are empty
  const isEmpty = (
    !rewards.credits && 
    !rewards.experience && 
    (!rewards.reputation || Object.keys(rewards.reputation).length === 0) &&
    (!rewards.items || rewards.items.length === 0) &&
    (!rewards.resources || Object.keys(rewards.resources).length === 0) &&
    (!rewards.intel || rewards.intel.length === 0)
  );

  // Function to render reputation changes
  const renderReputation = () => {
    if (!rewards.reputation || Object.keys(rewards.reputation).length === 0) {
      return null;
    }

    return Object.entries(rewards.reputation).map(([faction, amount]) => (
      <div key={faction} className={styles.rewardItem}>
        {showIcons && (
          <div className={clsx(styles.rewardIcon, {
            [styles.positiveIcon]: amount > 0,
            [styles.negativeIcon]: amount < 0
          })}>
            {amount > 0 ? 
              <IconArrowUp size={18} stroke={1.5} /> : 
              <IconArrowDown size={18} stroke={1.5} />
            }
          </div>
        )}
        <div className={styles.rewardContent}>
          <span className={clsx(
            styles.rewardValue, 
            {
              [styles.positiveValue]: amount > 0, 
              [styles.negativeValue]: amount < 0
            }
          )}>
            {amount > 0 ? '+' : ''}{amount}
          </span>
          {showLabels && (
            <span className={styles.rewardLabel}>{faction} Reputation</span>
          )}
        </div>
      </div>
    ));
  };

  // Function to render resource rewards
  const renderResources = () => {
    if (!rewards.resources || Object.keys(rewards.resources).length === 0) {
      return null;
    }

    return Object.entries(rewards.resources).map(([resource, amount]) => (
      <div key={resource} className={styles.rewardItem}>
        {showIcons && (
          <div className={styles.rewardIcon}>
            <IconDatabase size={18} stroke={1.5} />
          </div>
        )}
        <div className={styles.rewardContent}>
          <span className={styles.rewardValue}>{amount}</span>
          {showLabels && (
            <span className={styles.rewardLabel}>
              {resource.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
            </span>
          )}
        </div>
      </div>
    ));
  };

  // Function to render item rewards
  const renderItems = () => {
    if (!rewards.items || rewards.items.length === 0) {
      return null;
    }

    return (
      <div className={styles.rewardItem}>
        {showIcons && (
          <div className={styles.rewardIcon}>
            <IconPackage size={18} stroke={1.5} />
          </div>
        )}
        <div className={styles.rewardContent}>
          <span className={styles.rewardValue}>{rewards.items.length}</span>
          {showLabels && (
            <span className={styles.rewardLabel}>
              {rewards.items.length === 1 ? 'Item' : 'Items'}
            </span>
          )}
        </div>
      </div>
    );
  };

  // Function to render intel rewards
  const renderIntel = () => {
    if (!rewards.intel || rewards.intel.length === 0) {
      return null;
    }

    return (
      <div className={styles.rewardItem}>
        {showIcons && (
          <div className={styles.rewardIcon}>
            <IconInfoCircle size={18} stroke={1.5} />
          </div>
        )}
        <div className={styles.rewardContent}>
          <span className={styles.rewardValue}>{rewards.intel.length}</span>
          {showLabels && (
            <span className={styles.rewardLabel}>
              {rewards.intel.length === 1 ? 'Intel' : 'Intel Items'}
            </span>
          )}
        </div>
      </div>
    );
  };

  // Render compact version
  if (compact) {
    return (
      <div className={clsx(styles.rewardDisplayCompact, className)}>
        {rewards.credits && (
          <Badge colorScheme="success" variant="subtle" size="sm" className={styles.compactBadge}>
            <IconCoin size={14} className={styles.badgeIcon} stroke={1.5} />
            {rewards.credits}
          </Badge>
        )}
        
        {rewards.experience && (
          <Badge colorScheme="info" variant="subtle" size="sm" className={styles.compactBadge}>
            <IconStar size={14} className={styles.badgeIcon} stroke={1.5} />
            {rewards.experience} XP
          </Badge>
        )}
        
        {rewards.reputation && Object.entries(rewards.reputation).map(([faction, amount]) => (
          <Badge 
            key={faction}
            colorScheme={amount > 0 ? 'success' : 'danger'}
            variant="subtle" 
            size="sm"
            className={styles.compactBadge}
          >
            {amount > 0 ? 
              <IconArrowUp size={14} className={styles.badgeIcon} stroke={1.5} /> : 
              <IconArrowDown size={14} className={styles.badgeIcon} stroke={1.5} />
            }
            {faction} {amount > 0 ? '+' : ''}{amount}
          </Badge>
        ))}
        
        {rewards.items && rewards.items.length > 0 && (
          <Badge colorScheme="secondary" variant="subtle" size="sm" className={styles.compactBadge}>
            <IconPackage size={14} className={styles.badgeIcon} stroke={1.5} />
            {rewards.items.length} {rewards.items.length === 1 ? 'Item' : 'Items'}
          </Badge>
        )}
      </div>
    );
  }

  // Render full version
  return (
    <Card className={clsx(styles.rewardDisplay, className)} variant="default">
      {title && (
        <CardHeader 
          title={
            <div className={styles.headerTitle}>
              <IconTrophy size={18} className={styles.headerIcon} stroke={1.5} />
              {title}
            </div>
          }
        />
      )}
      <CardBody>
        {isEmpty ? (
          <div className={styles.emptyState}>
            <IconTrophy size={28} className={styles.emptyIcon} stroke={1.5} />
            <p className={styles.emptyText}>No rewards available</p>
          </div>
        ) : (
          <div className={styles.rewardList}>
            {rewards.credits && (
              <div className={styles.rewardItem}>
                {showIcons && (
                  <div className={styles.rewardIcon}>
                    <IconCoin size={18} stroke={1.5} />
                  </div>
                )}
                <div className={styles.rewardContent}>
                  <span className={styles.rewardValue}>{rewards.credits}</span>
                  {showLabels && (
                    <span className={styles.rewardLabel}>Credits</span>
                  )}
                </div>
              </div>
            )}
            
            {rewards.experience && (
              <div className={styles.rewardItem}>
                {showIcons && (
                  <div className={styles.rewardIcon}>
                    <IconStar size={18} stroke={1.5} />
                  </div>
                )}
                <div className={styles.rewardContent}>
                  <span className={styles.rewardValue}>{rewards.experience}</span>
                  {showLabels && (
                    <span className={styles.rewardLabel}>Experience</span>
                  )}
                </div>
              </div>
            )}
            
            {renderReputation()}
            {renderResources()}
            {renderItems()}
            {renderIntel()}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default RewardDisplay;