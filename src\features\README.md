# Features Layer

This layer contains business logic and UI components specific to individual features of the application.

## Structure

Features are organized by business domain and follow this structure:

```
features/
├── [feature-name]/     # Business feature
│   ├── ui/             # Feature-specific UI components
│   ├── model/          # Feature state (slices, actions, etc.)
│   ├── api/            # API interactions
│   ├── lib/            # Feature-specific utilities
│   └── index.ts        # Public API
```

## Current Features

- **mission** - Mission management, creation, filtering
- **specialist** - Specialist management, recruitment, development
- **terminal** - Terminal functionality and commands
- **tabs** - Tab system management
- **notifications** - System notifications
- **skill** - Skill system and progression
- **reward** - Reward display and management
- **training** - Training and development system

## Feature Principles

1. **Domain Isolation**: Features should focus on specific business domains
2. **Encapsulated Logic**: Business logic should be contained within the feature
3. **Clean API**: Features should export a clear public API
4. **UI Components**: Feature-specific UI components belong in the feature's ui/ directory
5. **State Management**: State directly related to the feature should be in the feature's model/ directory
