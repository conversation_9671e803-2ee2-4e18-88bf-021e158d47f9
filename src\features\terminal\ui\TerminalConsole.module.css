.terminalConsole {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-family: var(--font-mono);
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  position: relative;
}

/* Terminal scanline effect */
.terminalConsole::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    rgba(18, 16, 16, 0) 50%,
    rgba(0, 0, 0, 0.1) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  z-index: 2;
  opacity: 0.2;
}

/* Gentle CRT flicker animation */
.terminalConsole::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 240, 255, 0.03);
  pointer-events: none;
  z-index: 3;
  opacity: 0;
  animation: flicker 5s infinite;
}

@keyframes flicker {
  0% { opacity: 0; }
  5% { opacity: 0.1; }
  10% { opacity: 0; }
  15% { opacity: 0; }
  20% { opacity: 0.1; }
  25% { opacity: 0; }
  30% { opacity: 0; }
  35% { opacity: 0.05; }
  40% { opacity: 0; }
  45% { opacity: 0; }
  50% { opacity: 0.1; }
  55% { opacity: 0; }
  60% { opacity: 0; }
  65% { opacity: 0; }
  70% { opacity: 0.05; }
  75% { opacity: 0; }
  80% { opacity: 0; }
  85% { opacity: 0.1; }
  90% { opacity: 0; }
  95% { opacity: 0; }
  100% { opacity: 0; }
}

.outputArea {
  flex: 1;
  overflow-y: auto;
  white-space: pre-wrap;
  padding-right: 8px;
  position: relative;
  z-index: 4;
}

/* Scrollbar styling */
.outputArea::-webkit-scrollbar {
  width: 6px;
}

.outputArea::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.outputArea::-webkit-scrollbar-thumb {
  background: var(--color-accent-primary);
  border-radius: 3px;
}

.outputArea::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-primary-hover);
}

.welcomeMessage {
  margin-bottom: 16px;
  color: var(--color-accent-primary);
  text-shadow: 0 0 8px rgba(0, 240, 255, 0.5);
}

.systemLine {
  margin-bottom: 4px;
  position: relative;
  display: flex;
  align-items: center;
}

.systemLine .icon {
  margin-right: 8px;
  flex-shrink: 0;
}

.commandForm {
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.prompt {
  color: var(--color-accent-primary);
  margin-right: 8px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  text-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
}

.promptIcon {
  margin-right: 6px;
}

.commandInput {
  flex: 1;
  padding: 0;
  background-color: transparent;
  border: none;
  color: var(--color-text-primary);
  font-family: inherit;
  font-size: inherit;
  outline: none;
  caret-color: var(--color-accent-primary);
}

.commandOutput {
  margin: 8px 0;
  padding-left: 16px;
  border-left: 2px solid rgba(0, 240, 255, 0.2);
}

.commandText {
  color: var(--color-text-secondary);
  margin-bottom: 4px;
}

.outputText {
  color: var(--color-text-primary);
}

.errorText {
  color: var(--color-error);
}

.successText {
  color: var(--color-success);
}

.warningText {
  color: var(--color-warning);
}

.infoText {
  color: var(--color-info);
}

.toolbarArea {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 12px;
}

.toolbarButton {
  background: transparent;
  border: none;
  color: var(--color-text-secondary);
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.toolbarButton:hover {
  color: var(--color-text-primary);
  background-color: rgba(255, 255, 255, 0.05);
}

.toolbarButton:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 240, 255, 0.3);
}
