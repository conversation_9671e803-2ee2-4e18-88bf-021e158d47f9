/* Tooltip styles */

.tooltipWrapper {
  display: inline-block;
  position: relative;
}

.tooltip {
  position: absolute;
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  max-width: 250px;
  word-wrap: break-word;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.3));
  pointer-events: none;
}

.tooltipContent {
  position: relative;
  z-index: 1;
}

.tooltipArrow {
  position: absolute;
  width: 8px;
  height: 8px;
  transform: rotate(45deg);
}

/* Positioning */
.top .tooltipArrow {
  bottom: -4px;
  left: 50%;
  margin-left: -4px;
}

.bottom .tooltipArrow {
  top: -4px;
  left: 50%;
  margin-left: -4px;
}

.left .tooltipArrow {
  right: -4px;
  top: 50%;
  margin-top: -4px;
}

.right .tooltipArrow {
  left: -4px;
  top: 50%;
  margin-top: -4px;
}

/* Variants */
.default {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-primary);
}

.default .tooltipArrow {
  background-color: var(--color-bg-tertiary);
  border-right: 1px solid var(--color-border-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.info {
  background-color: var(--color-info-alpha);
  color: var(--color-text-on-accent);
  border: 1px solid var(--color-info);
}

.info .tooltipArrow {
  background-color: var(--color-info-alpha);
  border-right: 1px solid var(--color-info);
  border-bottom: 1px solid var(--color-info);
}

.success {
  background-color: var(--color-success-alpha);
  color: var(--color-text-on-accent);
  border: 1px solid var(--color-success);
}

.success .tooltipArrow {
  background-color: var(--color-success-alpha);
  border-right: 1px solid var(--color-success);
  border-bottom: 1px solid var(--color-success);
}

.warning {
  background-color: var(--color-warning-alpha);
  color: var(--color-text-on-accent);
  border: 1px solid var(--color-warning);
}

.warning .tooltipArrow {
  background-color: var(--color-warning-alpha);
  border-right: 1px solid var(--color-warning);
  border-bottom: 1px solid var(--color-warning);
}

.danger {
  background-color: var(--color-error-alpha);
  color: var(--color-text-on-accent);
  border: 1px solid var(--color-error);
}

.danger .tooltipArrow {
  background-color: var(--color-error-alpha);
  border-right: 1px solid var(--color-error);
  border-bottom: 1px solid var(--color-error);
}

/* Animation */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Position fixes for arrows based on placement */
.top .tooltipArrow {
  transform: rotate(225deg);
}

.bottom .tooltipArrow {
  transform: rotate(45deg);
}

.left .tooltipArrow {
  transform: rotate(315deg);
}

.right .tooltipArrow {
  transform: rotate(135deg);
}
