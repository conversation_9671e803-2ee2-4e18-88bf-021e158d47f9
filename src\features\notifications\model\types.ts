/**
 * Notification system types
 */

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: number;
  autoDismiss?: boolean;
  dismissAfter?: number; // in milliseconds
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: string;
}

export interface NotificationsState {
  notifications: Notification[];
}

export interface AddNotificationPayload {
  type: NotificationType;
  title: string;
  message: string;
  autoDismiss?: boolean;
  dismissAfter?: number;
  actions?: NotificationAction[];
}

export interface RemoveNotificationPayload {
  id: string;
}
