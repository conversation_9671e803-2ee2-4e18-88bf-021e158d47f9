/**
 * Notification system types
 */

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: number;
  autoDismiss?: boolean;
  dismissAfter?: number; // in milliseconds
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: string;
}

export interface NotificationsState {
  notifications: Notification[];
}

// Note: Payload types removed - Zustand actions use direct parameters
