import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { TerminalCommand, CommandOutput, TerminalInstance, TerminalState } from './types';

interface TerminalActions {
  executeCommand: (params: { 
    command: string;
    instanceId?: string;
    commandId: string;
  }) => void;
  setCommandOutput: (params: {
    commandId: string;
    output: string;
    status: 'success' | 'error' | 'info' | 'warning';
    instanceId?: string;
  }) => void;
  navigateHistory: (params: { 
    direction: 'up' | 'down';
    instanceId?: string;
  }) => void;
  clearHistory: (params: { instanceId?: string }) => void;
  clearTerminal: (params: { instanceId?: string }) => void;
  changeDirectory: (params: { 
    path: string;
    instanceId?: string;
  }) => void;
}

type TerminalStore = TerminalState & TerminalActions;

const initialState: TerminalState = {
  commands: [],
  outputs: [],
  instances: {
    // Default terminal instance
    'default': {
      history: [],
      currentDirectory: '/home/<USER>',
      commandIndex: -1,
    }
  }
};

export const useTerminalStore = create<TerminalStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      executeCommand: ({ command, instanceId = 'default', commandId }) => {
        set((state) => {
          // Ensure instance exists
          if (!state.instances[instanceId]) {
            state.instances[instanceId] = {
              history: [],
              currentDirectory: '/home/<USER>',
              commandIndex: -1,
            };
          }
          
          // Add command to global commands list
          const newCommand: TerminalCommand = {
            command,
            instanceId,
            commandId,
            timestamp: Date.now(),
          };
          
          // Add to instance history
          const instance = state.instances[instanceId];
          const newHistory = [...instance.history, command];
          
          return {
            ...state,
            commands: [...state.commands, newCommand],
            instances: {
              ...state.instances,
              [instanceId]: {
                ...instance,
                history: newHistory,
                commandIndex: newHistory.length,
              }
            }
          };
        });
      },
      
      setCommandOutput: ({ commandId, output, status, instanceId = 'default' }) => {
        set((state) => {
          const newOutput: CommandOutput = {
            commandId,
            output,
            status,
            instanceId,
            timestamp: Date.now(),
          };
          
          return {
            ...state,
            outputs: [...state.outputs, newOutput]
          };
        });
      },
      
      navigateHistory: ({ direction, instanceId = 'default' }) => {
        set((state) => {
          const instance = state.instances[instanceId];
          if (!instance) return state;
          
          const historyLength = instance.history.length;
          let newCommandIndex = instance.commandIndex;
          
          if (direction === 'up') {
            // Move back in history (older commands)
            if (instance.commandIndex > 0) {
              newCommandIndex--;
            }
          } else {
            // Move forward in history (newer commands)
            if (instance.commandIndex < historyLength) {
              newCommandIndex++;
            }
          }
          
          return {
            ...state,
            instances: {
              ...state.instances,
              [instanceId]: {
                ...instance,
                commandIndex: newCommandIndex
              }
            }
          };
        });
      },
      
      clearHistory: ({ instanceId }) => {
        set((state) => {
          if (instanceId) {
            // Clear specific instance
            if (state.instances[instanceId]) {
              return {
                ...state,
                commands: state.commands.filter(cmd => cmd.instanceId !== instanceId),
                outputs: state.outputs.filter(out => out.instanceId !== instanceId),
                instances: {
                  ...state.instances,
                  [instanceId]: {
                    ...state.instances[instanceId],
                    history: [],
                    commandIndex: -1,
                  }
                }
              };
            }
            return state;
          } else {
            // Clear all history
            const clearedInstances = Object.keys(state.instances).reduce((acc, id) => {
              acc[id] = {
                ...state.instances[id],
                history: [],
                commandIndex: -1,
              };
              return acc;
            }, {} as Record<string, TerminalInstance>);
            
            return {
              ...state,
              commands: [],
              outputs: [],
              instances: clearedInstances
            };
          }
        });
      },
      
      clearTerminal: ({ instanceId = 'default' }) => {
        set((state) => ({
          ...state,
          commands: state.commands.filter(cmd => cmd.instanceId !== instanceId),
          outputs: state.outputs.filter(out => out.instanceId !== instanceId)
        }));
      },
      
      changeDirectory: ({ path, instanceId = 'default' }) => {
        set((state) => {
          const instance = state.instances[instanceId];
          if (!instance) return state;
          
          return {
            ...state,
            instances: {
              ...state.instances,
              [instanceId]: {
                ...instance,
                currentDirectory: path
              }
            }
          };
        });
      },
    }),
    {
      name: 'terminal-store',
    }
  )
);
