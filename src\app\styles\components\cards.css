/* Card elements */

.cyber-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--card-padding);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

/* Card with top accent border */
.cyber-card-accent {
  position: relative;
}

.cyber-card-accent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--gradient-primary);
}

.cyber-card-accent-secondary::before {
  background: var(--gradient-secondary);
}

.cyber-card-accent-warning::before {
  background: var(--gradient-warn);
}

.cyber-card-accent-success::before {
  background: var(--gradient-success);
}

/* Glowing card */
.cyber-card-glow {
  border-color: var(--border-active);
  box-shadow: var(--shadow-neon);
}

.cyber-card-glow-pink {
  border-color: var(--neon-pink);
  box-shadow: var(--shadow-pink);
}

.cyber-card-glow-purple {
  border-color: var(--neon-purple);
  box-shadow: var(--shadow-purple);
}

.cyber-card-glow-yellow {
  border-color: var(--neon-yellow);
  box-shadow: var(--shadow-yellow);
}

/* Animated border */
.cyber-card-animated-border {
  animation: border-pulse 2s ease-in-out infinite;
}

/* Interactive card with hover effects */
.cyber-card-interactive {
  cursor: pointer;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.cyber-card-interactive:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.cyber-card-interactive:active {
  transform: translateY(-2px);
}

/* Card with detailed circuit background */
.cyber-card-circuit {
  position: relative;
}

.cyber-card-circuit::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10 10 H 90 V 90 H 10 L 10 10' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M30 10 V 30 H 10' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M70 10 V 30 H 90' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M30 90 V 70 H 10' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M70 90 V 70 H 90' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M10 50 H 30' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M90 50 H 70' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M50 10 V 30' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Cpath d='M50 90 V 70' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3Ccircle cx='50' cy='50' r='5' fill='none' stroke='%2300f0ff' stroke-width='0.5' stroke-opacity='0.1'/%3E%3C/svg%3E");
  opacity: 0.15;
  z-index: -1;
  pointer-events: none;
}

/* Card with data flow animation */
.cyber-card-data-flow {
  position: relative;
  overflow: hidden;
}

.cyber-card-data-flow::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    var(--bg-secondary) 0%, 
    var(--bg-secondary) 45%, 
    rgba(0, 240, 255, 0.2) 50%,
    var(--bg-secondary) 55%, 
    var(--bg-secondary) 100%);
  background-size: 200% 100%;
  animation: dataFlow 2s linear infinite;
  z-index: -1;
  pointer-events: none;
}

/* Card with scan line effect */
.cyber-card-scan {
  position: relative;
  overflow: hidden;
}

.cyber-card-scan::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(to bottom, 
    rgba(0, 240, 255, 0.3),
    rgba(0, 240, 255, 0));
  z-index: 1;
  animation: scan 2s ease-in-out infinite alternate;
  pointer-events: none;
}

/* Glass card effect */
.cyber-card-glass {
  background: rgba(19, 26, 41, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 240, 255, 0.2);
}

/* Dark elevated card */
.cyber-card-elevated {
  background-color: var(--bg-elevated);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* Card layouts */
.cyber-card-header {
  margin: calc(-1 * var(--card-padding));
  margin-bottom: var(--card-padding);
  padding: calc(var(--card-padding) * 0.75) var(--card-padding);
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.2);
}

.cyber-card-footer {
  margin: calc(-1 * var(--card-padding));
  margin-top: var(--card-padding);
  padding: calc(var(--card-padding) * 0.75) var(--card-padding);
  border-top: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.2);
}

.cyber-card-title {
  font-family: var(--font-display);
  font-size: var(--font-size-lg);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5em;
}

.cyber-card-content {
  padding: calc(var(--card-padding) * 0.5) 0;
}

/* Card component sizing */
.cyber-card-sm {
  padding: calc(var(--card-padding) * 0.75);
}

.cyber-card-lg {
  padding: calc(var(--card-padding) * 1.5);
}

/* Card grid layout */
.cyber-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-md);
}
