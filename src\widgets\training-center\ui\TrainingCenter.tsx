import React from 'react';
import { SkillDisplay } from '../../../features/skill/ui/skill-display';
import { SkillTree } from '../../../features/skill/ui/skill-tree';

interface TrainingCenterProps {
  className?: string;
}

const TrainingCenter: React.FC<TrainingCenterProps> = ({ className }) => {
  const [selectedSkillId, setSelectedSkillId] = React.useState<string | null>(null);

  return (
    <div className={`training-center ${className || ''}`}>
      <div className="training-center__header">
        <h1>Training Center</h1>
      </div>
      
      <div className="training-center__content">
        <div className="training-center__skill-tree">
          <SkillTree 
            onSelectSkill={(skillId) => setSelectedSkillId(skillId)}
            selectedSkillId={selectedSkillId}
          />
        </div>
        
        {selectedSkillId && (
          <div className="training-center__skill-detail">
            <SkillDisplay skillId={selectedSkillId} />
          </div>
        )}
      </div>
    </div>
  );
};

export default TrainingCenter;