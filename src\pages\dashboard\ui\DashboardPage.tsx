import React from 'react';
import { useAppDispatch } from '../../../app/store';
import { addTab } from '../../../features/tabs/model/slice';
import styles from './DashboardPage.module.css';
import Button from '../../../shared/ui/Button';
import { IconTerminal2, IconTarget, IconShoppingCart, IconUsers } from '@tabler/icons-react';

interface DashboardPageProps {
  tabId: string;
}

const DashboardPage: React.FC<DashboardPageProps> = ({ tabId }) => {
  const dispatch = useAppDispatch();
  
  // Mock data for dashboard display
  const activeMissions = [
    { id: 'mission-1', title: 'Data Extraction', faction: 'Crime Syndicate', difficulty: 'professional', progress: 60 },
    { id: 'mission-2', title: 'System Infiltration', faction: 'State-Sponsored', difficulty: 'expert', progress: 25 },
  ];
  
  // Mock recent events
  const recentEvents = [
    { id: 'event-1', title: 'New vulnerability discovered', time: '10:23', type: 'info' },
    { id: 'event-2', title: 'Team member completed training', time: '09:15', type: 'success' },
    { id: 'event-3', title: 'Security breach detected', time: 'Yesterday', type: 'warning' },
  ];
  
  // Open terminal tab
  const openTerminal = () => {
    dispatch(addTab({
      id: 'terminal-' + Date.now(),
      title: 'Terminal',
      closable: true,
      type: 'terminal',
    }));
  };

  // Open marketplace tab
  const openMarketplace = () => {
    dispatch(addTab({
      id: 'market-' + Date.now(),
      title: 'Marketplace',
      closable: true,
      type: 'market',
    }));
  };

  // Open missions tab
  const openMissions = () => {
    dispatch(addTab({
      id: 'mission-' + Date.now(),
      title: 'Missions',
      closable: true,
      type: 'mission',
    }));
  };

  // Open team tab
  const openTeam = () => {
    dispatch(addTab({
      id: 'team-' + Date.now(),
      title: 'Team',
      closable: true,
      type: 'team',
    }));
  };
  
  return (
    <div className={styles.dashboardPage}>
      <div className={styles.dashboardHeader}>
        <h1 className={styles.dashboardTitle}>Operations Dashboard</h1>
        <span className={styles.welcomeMessage}>Welcome back, Operator</span>
      </div>
      
      <div className={styles.dashboardContent}>
        {/* Quick actions section */}
        <section className={styles.quickActions}>
          <h2 className={styles.sectionTitle}>Quick Actions</h2>
          <div className={styles.actionButtons}>
            <Button 
              variant="cyberpunk" 
              size="md" 
              icon={<IconTerminal2 size={18} />}
              onClick={openTerminal}
            >
              Terminal
            </Button>
            <Button 
              variant="cyberpunk" 
              size="md" 
              icon={<IconTarget size={18} />}
              onClick={openMissions}
            >
              Missions
            </Button>
            <Button 
              variant="cyberpunk" 
              size="md" 
              icon={<IconShoppingCart size={18} />}
              onClick={openMarketplace}
            >
              Marketplace
            </Button>
            <Button 
              variant="cyberpunk" 
              size="md" 
              icon={<IconUsers size={18} />}
              onClick={openTeam}
            >
              Team
            </Button>
          </div>
        </section>
        
        <div className={styles.dashboardGrid}>
          {/* Active missions section */}
          <section className={styles.activeMissions}>
            <h2 className={styles.sectionTitle}>Active Missions</h2>
            {activeMissions.length === 0 ? (
              <p className={styles.emptyState}>No active missions</p>
            ) : (
              <div className={styles.missionsList}>
                {activeMissions.map(mission => (
                  <div key={mission.id} className={styles.missionCard}>
                    <div className={styles.missionHeader}>
                      <h3 className={styles.missionTitle}>{mission.title}</h3>
                      <span className={`${styles.missionDifficulty} ${styles[mission.difficulty]}`}>
                        {mission.difficulty}
                      </span>
                    </div>
                    <div className={styles.missionDetails}>
                      <span className={styles.missionFaction}>{mission.faction}</span>
                      <div className={styles.progressContainer}>
                        <div className={styles.progressLabel}>Progress: {mission.progress}%</div>
                        <div className={styles.progressBar}>
                          <div 
                            className={styles.progressFill} 
                            style={{ width: `${mission.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </section>
          
          {/* Recent events section */}
          <section className={styles.recentEvents}>
            <h2 className={styles.sectionTitle}>Recent Events</h2>
            {recentEvents.length === 0 ? (
              <p className={styles.emptyState}>No recent events</p>
            ) : (
              <div className={styles.eventsList}>
                {recentEvents.map(event => (
                  <div key={event.id} className={`${styles.eventItem} ${styles[event.type]}`}>
                    <span className={styles.eventTime}>{event.time}</span>
                    <span className={styles.eventTitle}>{event.title}</span>
                  </div>
                ))}
              </div>
            )}
          </section>
          
          {/* Resource summary section */}
          <section className={styles.resourceSummary}>
            <h2 className={styles.sectionTitle}>Resources</h2>
            <div className={styles.resourceGrid}>
              <div className={styles.resourceCard}>
                <span className={styles.resourceValue}>1,000</span>
                <span className={styles.resourceName}>Credits</span>
              </div>
              <div className={styles.resourceCard}>
                <span className={styles.resourceValue}>0</span>
                <span className={styles.resourceName}>Data</span>
              </div>
              <div className={styles.resourceCard}>
                <span className={styles.resourceValue}>3</span>
                <span className={styles.resourceName}>Access Keys</span>
              </div>
              <div className={styles.resourceCard}>
                <span className={styles.resourceValue}>85/100</span>
                <span className={styles.resourceName}>Energy</span>
              </div>
            </div>
          </section>
          
          {/* Faction standings section */}
          <section className={styles.factionStandings}>
            <h2 className={styles.sectionTitle}>Faction Standings</h2>
            <div className={styles.standingsList}>
              <div className={styles.standingItem}>
                <span className={styles.factionName}>Crime Syndicate</span>
                <div className={styles.standingBar}>
                  <div 
                    className={`${styles.standingFill} ${styles.positive}`} 
                    style={{ width: '65%' }}
                  ></div>
                </div>
                <span className={styles.standingValue}>+65</span>
              </div>
              <div className={styles.standingItem}>
                <span className={styles.factionName}>State-Sponsored</span>
                <div className={styles.standingBar}>
                  <div 
                    className={`${styles.standingFill} ${styles.positive}`} 
                    style={{ width: '30%' }}
                  ></div>
                </div>
                <span className={styles.standingValue}>+30</span>
              </div>
              <div className={styles.standingItem}>
                <span className={styles.factionName}>Hacktivists</span>
                <div className={styles.standingBar}>
                  <div 
                    className={`${styles.standingFill} ${styles.negative}`} 
                    style={{ width: '20%' }}
                  ></div>
                </div>
                <span className={styles.standingValue}>-20</span>
              </div>
              <div className={styles.standingItem}>
                <span className={styles.factionName}>Security Operations</span>
                <div className={styles.standingBar}>
                  <div 
                    className={`${styles.standingFill} ${styles.positive}`} 
                    style={{ width: '10%' }}
                  ></div>
                </div>
                <span className={styles.standingValue}>+10</span>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
