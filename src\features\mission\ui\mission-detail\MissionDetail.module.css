/* MissionDetail styles */

.missionDetail {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* Header Styles */
.headerTitle {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.backButton {
  margin-right: var(--space-sm);
}

.headerSubtitle {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

/* Mission Content */
.missionContent {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  overflow-y: auto;
  padding: var(--space-sm);
}

/* Mission Brief */
.briefSection {
  margin-bottom: var(--space-md);
}

.sectionTitle {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-sm);
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--color-text-primary);
}

.sectionHeading {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.sectionIcon {
  color: var(--color-accent-primary);
}

.briefCard {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-border-primary);
  position: relative;
  overflow: hidden;
}

.briefCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at top right, rgba(0, 240, 255, 0.05), transparent 70%);
  pointer-events: none;
}

.briefText {
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: 1.6;
  color: var(--color-text-secondary);
  white-space: pre-line;
}

/* Layout */
.columnsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
}

@media (max-width: 768px) {
  .columnsContainer {
    grid-template-columns: 1fr;
  }
}

.detailsSection, .rewardsSection {
  height: 100%;
}

/* Details Section */
.details {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.detailRow {
  display: flex;
  align-items: flex-start;
  padding: var(--space-xs) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detailRow:last-child {
  border-bottom: none;
}

.label {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  flex: 0 0 120px;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-family: var(--font-mono);
}

.detailIcon {
  color: var(--color-accent-primary);
  opacity: 0.8;
}

.value {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

/* Risk Level */
.riskStars {
  display: flex;
  align-items: center;
  gap: 2px;
}

.riskStar {
  font-size: var(--font-size-md);
  line-height: 1;
}

.filledStar {
  color: var(--color-warning);
  text-shadow: 0 0 8px rgba(255, 149, 0, 0.4);
}

.emptyStar {
  color: var(--color-text-disabled);
}

.riskValue {
  margin-left: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
}

.skillsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

/* Rewards Section */
.rewardsList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.rewardItem {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-xs) var(--space-sm);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
  transition: transform 0.2s ease;
}

.rewardItem:hover {
  transform: translateX(4px);
}

.rewardIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
}

.positiveIcon {
  color: var(--color-success);
}

.negativeIcon {
  color: var(--color-error);
}

.rewardValue {
  font-weight: 600;
  font-size: var(--font-size-md);
  min-width: 50px;
  flex-shrink: 0;
  font-family: var(--font-mono);
}

.positiveValue {
  color: var(--color-success);
}

.negativeValue {
  color: var(--color-error);
}

.rewardLabel {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Objectives Section */
.objectivesList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.objectiveItem {
  padding: var(--space-md);
  border-radius: var(--border-radius-md);
  background-color: var(--color-bg-tertiary);
  border-left: 4px solid var(--color-border-primary);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.objectiveItem:hover {
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.objectiveItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at top right, rgba(255, 255, 255, 0.03), transparent 70%);
  pointer-events: none;
}

.objectiveItem.completed {
  border-left-color: var(--color-success);
}

.objectiveItem.in_progress {
  border-left-color: var(--color-info);
}

.objectiveItem.failed {
  border-left-color: var(--color-error);
}

.objectiveHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.objectiveTitle {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.requiredBadge {
  margin-left: var(--space-xs);
}

.objectiveDescription {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.5;
}

/* Action Buttons */
.startButton {
  position: relative;
  overflow: hidden;
}

.startButton::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.startButton:hover::after {
  opacity: 1;
}

/* Scrollbar styling */
.missionContent::-webkit-scrollbar {
  width: 6px;
}

.missionContent::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.missionContent::-webkit-scrollbar-thumb {
  background: var(--color-accent-primary);
  border-radius: 3px;
}

.missionContent::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-primary-hover);
}