/* Scanlines effect for cyberpunk aesthetic */

/* Basic scanlines overlay */
.scanlines {
  position: relative;
  overflow: hidden;
}

.scanlines::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent 50%,
    rgba(0, 0, 0, 0.05) 50%
  );
  background-size: 100% 4px;
  z-index: 2;
  pointer-events: none;
  opacity: 0.3;
}

/* Full-screen scanlines (applied to body) */
.scanlines-screen::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent 50%,
    rgba(0, 0, 0, 0.05) 50%
  );
  background-size: 100% 4px;
  z-index: 2000;
  pointer-events: none;
  opacity: 0.4;
}

/* Add slight noise texture */
.scanlines-screen::after,
.scanlines::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/png;base64,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');
  z-index: 2001;
  pointer-events: none;
  opacity: 0.03;
}

/* Animated scanline (for terminal/screen effect) */
@keyframes scan {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 0% 100%;
  }
}

.scanlines-animated {
  position: relative;
  overflow: hidden;
}

.scanlines-animated::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(to bottom, 
    rgba(0, 240, 255, 0.3),
    rgba(0, 240, 255, 0));
  z-index: 2;
  animation: scan 2s ease-in-out infinite alternate;
  pointer-events: none;
}

/* Horizontal scan effect */
@keyframes h-scan {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 0%;
  }
}

.scanlines-h-animated {
  position: relative;
  overflow: hidden;
}

.scanlines-h-animated::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 8px;
  background: linear-gradient(to right, 
    rgba(0, 240, 255, 0.3),
    rgba(0, 240, 255, 0));
  z-index: 2;
  animation: h-scan 2s ease-in-out infinite alternate;
  pointer-events: none;
}

/* CRT power off/on animation */
@keyframes crt-on {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
    filter: brightness(3) saturate(0);
  }
  30% {
    opacity: 1;
    transform: scale(1.05) translateY(-10px);
    filter: brightness(2) saturate(0.2);
  }
  40% {
    transform: scale(0.98) translateY(5px);
    filter: brightness(1.5) saturate(0.5);
  }
  60% {
    transform: scale(1.01) translateY(-2px);
    filter: brightness(1.2) saturate(0.8);
  }
  100% {
    transform: scale(1) translateY(0);
    filter: brightness(1) saturate(1);
  }
}

.crt-on {
  animation: crt-on 0.6s ease-in-out;
}

/* Screen flicker animation */
@keyframes flicker {
  0%, 100% { opacity: 1; }
  92% { opacity: 1; }
  93% { opacity: 0.6; }
  94% { opacity: 1; }
  95% { opacity: 0.9; }
  96% { opacity: 1; }
  97% { opacity: 0.9; }
  98% { opacity: 1; }
  99% { opacity: 0.8; }
}

.flicker {
  animation: flicker 10s infinite ease-in-out;
}

/* Bad reception effect */
@keyframes bad-reception {
  0% { transform: translateX(0); }
  5% { transform: translateX(-5px); }
  10% { transform: translateX(5px); }
  15% { transform: translateX(-3px); }
  20% { transform: translateX(3px); }
  25% { transform: translateX(0); }
  90% { transform: translateX(0); }
  95% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}

.bad-reception {
  position: relative;
}

.bad-reception::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.05);
  animation: bad-reception 15s infinite linear;
  pointer-events: none;
}