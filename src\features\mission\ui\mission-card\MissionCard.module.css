/* MissionCard styles */

/* Common styles */
.fullCard {
  width: 100%;
  max-width: 600px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.fullCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(0, 240, 255, 0),
    rgba(0, 240, 255, 0.5),
    rgba(0, 240, 255, 0)
  );
  z-index: 1;
}

.fullCard:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 5px 20px rgba(0, 240, 255, 0.15),
    0 0 0 1px rgba(0, 240, 255, 0.2);
}

.fullCard:hover::before {
  animation: glow 1.5s ease infinite;
}

@keyframes glow {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.compactCard {
  width: 100%;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.compactCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 240, 255, 0.1);
}

.compactCardBody {
  padding: var(--space-sm);
}

/* Header styles */
.headerRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.titleWrapper {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  width: 100%;
}

.missionTitle {
  color: var(--color-text-primary);
  font-weight: 600;
  font-size: var(--font-size-lg);
  text-shadow: 0 0 10px rgba(0, 240, 255, 0.2);
  position: relative;
  display: inline-block;
}

.missionTitle::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -3px;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(0, 240, 255, 0),
    rgba(0, 240, 255, 0.5),
    rgba(0, 240, 255, 0)
  );
}

.subtitleWrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.badgeContainer {
  display: flex;
  gap: var(--space-xs);
}

.compactTitle {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--color-text-primary);
  text-shadow: 0 0 8px rgba(0, 240, 255, 0.15);
}

/* Faction styles */
.factionRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-xs);
  gap: var(--space-xs);
}

.factionName {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: 500;
  font-family: var(--font-headers);
  letter-spacing: 0.5px;
}

/* Description */
.description {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  position: relative;
  padding: var(--space-sm);
  border-left: 2px solid var(--color-accent-primary);
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: var(--border-radius-sm);
}

/* Details section */
.details {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
  background-color: rgba(0, 0, 0, 0.15);
  padding: var(--space-sm);
  border-radius: var(--border-radius-sm);
  border: 1px solid rgba(0, 240, 255, 0.1);
}

.detailRow {
  display: flex;
  align-items: flex-start;
  padding: var(--space-xs) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detailRow:last-child {
  border-bottom: none;
}

.label {
  flex: 0 0 120px;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  font-family: var(--font-mono);
}

.value {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
}

.inlineIcon {
  margin-right: var(--space-xs);
  vertical-align: text-bottom;
  opacity: 0.8;
}

/* Risk level display */
.riskLevelContainer {
  display: flex;
  align-items: center;
  gap: 4px;
}

.riskStar {
  font-size: var(--font-size-md);
  line-height: 1;
  transition: all 0.2s ease;
}

.filledStar {
  color: var(--color-warning);
  text-shadow: 0 0 8px rgba(255, 245, 0, 0.4);
}

.emptyStar {
  color: var(--color-text-disabled);
}

.riskValue {
  margin-left: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
}

.skillsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

/* Rewards section */
.rewards {
  margin-top: var(--space-md);
  padding: var(--space-sm);
  border-top: 1px solid var(--color-border-primary);
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: var(--border-radius-sm);
  position: relative;
  overflow: hidden;
}

.rewards::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at top right, rgba(255, 233, 125, 0.1), transparent 70%);
  pointer-events: none;
}

.rewardsLabel {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-sm);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-text-primary);
  font-family: var(--font-headers);
  letter-spacing: 0.5px;
}

.rewardsIcon {
  color: var(--color-warning);
  filter: drop-shadow(0 0 5px rgba(255, 245, 0, 0.4));
}

.rewardsList {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.rewardItem {
  margin-right: 0;
  transition: transform 0.15s ease;
}

.rewardItem:hover {
  transform: translateY(-2px);
}

/* Expiry warning */
.expiryWarning {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-top: var(--space-xs);
  font-size: var(--font-size-xs);
  color: var(--color-warning);
  background-color: rgba(255, 149, 0, 0.1);
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  animation: pulsate 2s infinite ease-in-out;
}

@keyframes pulsate {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.expiryBanner {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
  padding: var(--space-xs) var(--space-sm);
  background-color: rgba(255, 149, 0, 0.1);
  border-left: 3px solid var(--color-warning);
  color: var(--color-warning);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-sm);
  animation: pulsate 2s infinite ease-in-out;
}