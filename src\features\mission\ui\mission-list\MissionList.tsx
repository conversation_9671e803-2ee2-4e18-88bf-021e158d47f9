import React, { useState } from 'react';
import { Mission, MissionDifficulty, MissionType, MissionCategory } from '@/core/mission/types';
import {MissionCard} from '../mission-card/MissionCard';
import { Input, <PERSON><PERSON>, <PERSON><PERSON>, Card, CardHeader, CardBody } from '@/shared/ui';
import styles from './MissionList.module.css';
import {
  IconSearch,
  IconFilter,
  IconLoader,
  IconFolder,
  IconSortAscending,
  IconChevronDown,
  IconRefresh,
  IconX,
  IconCircleCheck
} from '@tabler/icons-react';
import clsx from 'clsx';

export interface MissionListFilterProps {
  difficulty?: MissionDifficulty | 'all';
  type?: MissionType | 'all';
  category?: MissionCategory | 'all';
  faction?: string | 'all';
  search?: string;
}

interface MissionListProps {
  missions: Mission[];
  onMissionSelect: (missionId: string) => void;
  onFilterChange?: (filters: MissionListFilterProps) => void;
  filters?: MissionListFilterProps;
  compact?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  showFilters?: boolean;
  className?: string;
  title?: string;
}

export const MissionList: React.FC<MissionListProps> = ({
  missions,
  onMissionSelect,
  onFilterChange,
  filters = {},
  compact = false,
  loading = false,
  emptyMessage = 'No missions available',
  showFilters = true,
  className,
  title = 'Available Missions'
}) => {
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onFilterChange) {
      onFilterChange({
        ...filters,
        search: e.target.value
      });
    }
  };

  const handleFilterClear = () => {
    if (onFilterChange) {
      onFilterChange({
        difficulty: 'all',
        type: 'all',
        category: 'all',
        faction: 'all',
        search: ''
      });
    }
  };
  
  const toggleFilterPanel = () => {
    setShowFilterPanel(!showFilterPanel);
  };

  const renderActiveFilters = () => {
    const activeFilters = [];
    
    if (filters.difficulty && filters.difficulty !== 'all') {
      activeFilters.push(
        <Badge key="difficulty" colorScheme="info" variant="subtle" size="sm">
          <span className={styles.badgeContent}>
            <span className={styles.badgeLabel}>Difficulty:</span>
            {filters.difficulty}
            <IconX size={12} className={styles.badgeIcon} stroke={2} />
          </span>
        </Badge>
      );
    }
    
    if (filters.type && filters.type !== 'all') {
      activeFilters.push(
        <Badge key="type" colorScheme="info" variant="subtle" size="sm">
          <span className={styles.badgeContent}>
            <span className={styles.badgeLabel}>Type:</span>
            {filters.type.replace('_', ' ')}
            <IconX size={12} className={styles.badgeIcon} stroke={2} />
          </span>
        </Badge>
      );
    }
    
    if (filters.category && filters.category !== 'all') {
      activeFilters.push(
        <Badge key="category" colorScheme="info" variant="subtle" size="sm">
          <span className={styles.badgeContent}>
            <span className={styles.badgeLabel}>Category:</span>
            {filters.category}
            <IconX size={12} className={styles.badgeIcon} stroke={2} />
          </span>
        </Badge>
      );
    }
    
    if (filters.faction && filters.faction !== 'all') {
      activeFilters.push(
        <Badge key="faction" colorScheme="info" variant="subtle" size="sm">
          <span className={styles.badgeContent}>
            <span className={styles.badgeLabel}>Faction:</span>
            {filters.faction}
            <IconX size={12} className={styles.badgeIcon} stroke={2} />
          </span>
        </Badge>
      );
    }
    
    if (activeFilters.length === 0) return null;
    
    return (
      <div className={styles.activeFilters}>
        <span className={styles.activeFiltersLabel}>Active Filters:</span>
        <div className={styles.filterBadges}>
          {activeFilters}
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleFilterClear}
            leftIcon={<IconRefresh size={14} stroke={1.5} />}
          >
            Reset
          </Button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={clsx(styles.container, className)}>
        <div className={styles.loadingContainer}>
          <IconLoader className={styles.loadingSpinner} size={32} stroke={1.5} />
          <p className={styles.loadingText}>Loading missions...</p>
        </div>
      </div>
    );
  }
  
  return (
    <Card className={clsx(styles.container, className)} variant="default">
      <CardHeader title={title} />
      <CardBody>
        {showFilters && (
          <div className={styles.filtersSection}>
            <div className={styles.searchWrapper}>
              <Input
                placeholder="Search missions..."
                value={filters.search || ''}
                onChange={handleSearchChange}
                leftIcon={<IconSearch size={18} stroke={1.5} />}
                size="md"
                isFullWidth
              />
            </div>
            
            <div className={styles.filterActions}>
              <Button 
                variant="secondary" 
                leftIcon={<IconFilter size={18} stroke={1.5} />}
                rightIcon={<IconChevronDown size={16} stroke={1.5} className={clsx(styles.dropdownIcon, { [styles.dropdownIconOpen]: showFilterPanel })} />}
                size="sm"
                onClick={toggleFilterPanel}
              >
                Filter
              </Button>
              <Button 
                variant="ghost" 
                leftIcon={<IconSortAscending size={18} stroke={1.5} />}
                size="sm"
              >
                Sort
              </Button>
            </div>
            
            {showFilterPanel && (
              <div className={styles.filterPanel}>
                {/* Filter panel content would go here */}
                <div className={styles.filterPanelSection}>
                  <h4 className={styles.filterPanelTitle}>Difficulty</h4>
                  <div className={styles.filterPanelOptions}>
                    {['all', 'novice', 'professional', 'expert', 'elite', 'legendary'].map(difficulty => (
                      <div 
                        key={difficulty} 
                        className={clsx(styles.filterOption, {
                          [styles.filterOptionActive]: filters.difficulty === difficulty
                        })}
                        onClick={() => onFilterChange && onFilterChange({...filters, difficulty: difficulty as MissionDifficulty | 'all'})}
                      >
                        {filters.difficulty === difficulty && (
                          <IconCircleCheck size={16} className={styles.filterOptionIcon} stroke={1.5} />
                        )}
                        <span className={styles.filterOptionLabel}>
                          {difficulty === 'all' ? 'All Difficulties' : difficulty}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {renderActiveFilters()}
          </div>
        )}
        
        {!missions || missions.length === 0 ? (
          <div className={styles.emptyContainer}>
            <IconFolder className={styles.emptyIcon} size={48} stroke={1} />
            <p className={styles.emptyMessage}>{emptyMessage}</p>
            <Button 
              variant="secondary" 
              size="sm"
              leftIcon={<IconRefresh size={16} stroke={1.5} />}
              onClick={handleFilterClear}
            >
              Reset Filters
            </Button>
          </div>
        ) : (
          <div className={styles.list}>
            {missions.map(mission => (
              <div key={mission.id} className={styles.missionItem}>
                <MissionCard
                  mission={mission}
                  compact={compact}
                  onClick={() => onMissionSelect(mission.id)}
                />
              </div>
            ))}
          </div>
        )}
      </CardBody>
    </Card>
  );
};
