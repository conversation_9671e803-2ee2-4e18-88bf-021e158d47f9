import React from 'react';
import { useAppSelector, useAppDispatch } from '../../../app/store';
import { closeTab, setActiveTab } from '../../../features/tabs/model/slice';
import styles from './TabBar.module.css';
import clsx from 'clsx';
import {
  IconLayoutDashboard,
  IconTerminal,
  IconTarget,
  IconShoppingCart,
  IconUsers,
  IconSearch,
  IconMessage,
  IconSettings,
  IconFile,
  IconX,
  IconPlus
} from '@tabler/icons-react';

// Icons for different tab types
const tabIcons: Record<string, React.ReactNode> = {
  dashboard: <IconLayoutDashboard size={18} stroke={1.5} />,
  terminal: <IconTerminal size={18} stroke={1.5} />,
  mission: <IconTarget size={18} stroke={1.5} />,
  market: <IconShoppingCart size={18} stroke={1.5} />,
  team: <IconUsers size={18} stroke={1.5} />,
  intel: <IconSearch size={18} stroke={1.5} />,
  messaging: <IconMessage size={18} stroke={1.5} />,
  settings: <IconSettings size={18} stroke={1.5} />,
};

const MAX_TABS = 4; // Maximum number of tabs that can be open at once

const TabBar: React.FC = () => {
  const dispatch = useAppDispatch();
  const { tabs, activeTabId } = useAppSelector(state => state.tabs);
  
  const handleTabClick = (tabId: string) => {
    dispatch(setActiveTab(tabId));
  };
  
  const handleCloseTab = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    dispatch(closeTab(tabId));
  };
  
  // Handler for the "+" button to show app launcher (to be implemented)
  const handleAddTab = () => {
    // Only allow adding a new tab if we're under the max limit
    if (tabs.length >= MAX_TABS) {
      // Show a notification that max tabs are reached
      console.warn(`Maximum of ${MAX_TABS} tabs allowed at once`);
      return;
    }
    
    // Open an app launcher modal or menu
    console.log('Open app launcher');
    // For now, this is just a placeholder
  };
  
  return (
    <div className={styles.tabBar}>
      <div className={styles.tabsList}>
        {tabs.map(tab => (
          <div 
            key={tab.id}
            className={clsx(
              styles.tab,
              activeTabId === tab.id && styles.active
            )}
            onClick={() => handleTabClick(tab.id)}
          >
            <span className={styles.tabIcon}>
              {tab.icon || tabIcons[tab.type] || <IconFile size={18} stroke={1.5} />}
            </span>
            <span className={styles.tabTitle}>{tab.title}</span>
            {tab.closable && (
              <button 
                className={styles.closeButton}
                onClick={(e) => handleCloseTab(e, tab.id)}
                aria-label={`Close ${tab.title}`}
              >
                <IconX size={14} stroke={1.5} />
              </button>
            )}
          </div>
        ))}
      </div>
      
      {tabs.length < MAX_TABS && (
        <button 
          className={styles.addTabButton} 
          onClick={handleAddTab}
          aria-label="Add new tab"
          title={`Add new tab (${tabs.length}/${MAX_TABS})`}
        >
          <IconPlus size={18} stroke={1.5} />
        </button>
      )}
    </div>
  );
};

export default TabBar;