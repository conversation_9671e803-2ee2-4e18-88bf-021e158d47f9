import React from 'react';
import styles from './Badge.module.css';
import clsx from 'clsx';

export type BadgeVariant = 'solid' | 'outline' | 'subtle';
export type BadgeColorScheme = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'gray';
export type BadgeSize = 'sm' | 'md' | 'lg';

export interface BadgeProps {
  children: React.ReactNode;
  variant?: BadgeVariant;
  colorScheme?: BadgeColorScheme;
  size?: BadgeSize;
  isRounded?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'solid',
  colorScheme = 'primary',
  size = 'md',
  isRounded = false,
  leftIcon,
  rightIcon,
  className,
  onClick,
}) => {
  const badgeClasses = clsx(
    styles.badge,
    styles[`variant-${variant}`],
    styles[`colorScheme-${colorScheme}`],
    styles[`size-${size}`],
    isRounded && styles.rounded,
    onClick && styles.clickable,
    className
  );

  return (
    <span className={badgeClasses} onClick={onClick}>
      {leftIcon && <span className={styles.leftIcon}>{leftIcon}</span>}
      {children}
      {rightIcon && <span className={styles.rightIcon}>{rightIcon}</span>}
    </span>
  );
};

export default Badge;