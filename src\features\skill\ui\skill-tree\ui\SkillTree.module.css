/* SkillTree styles */

.skillTree {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header styling */
.headerTitle {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.headerIcon {
  color: var(--color-accent-primary);
}

.headerPoints {
  margin-top: var(--space-xs);
}

.badgeIcon {
  margin-right: 4px;
}

.categoryFilters {
  display: flex;
  gap: var(--space-xs);
  flex-wrap: wrap;
}

/* Main content layout */
.skillTreeContent {
  display: flex;
  height: 100%;
  gap: var(--space-md);
  overflow: hidden;
}

.treeContainer {
  flex: 1;
  min-width: 0;
  overflow: auto;
  position: relative;
  border: 1px solid var(--color-border-primary);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-bg-tertiary);
}

/* Tree view with nodes and connections */
.treeView {
  position: relative;
  min-height: 400px;
  min-width: 100%;
  padding: var(--space-xl);
}

/* Skill nodes */
.skillNode {
  position: absolute;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  position: relative;
}

.skillNode:hover {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.4);
}

.skillNodeIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-primary);
}

.skillLevel {
  position: absolute;
  bottom: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--color-bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--color-text-primary);
  border: 1px solid currentColor;
}

/* Skill node states */
.unlockedSkill {
  background-color: var(--color-success-muted);
  border: 2px solid var(--color-success);
  color: var(--color-success);
}

.unlockedSkill .skillNodeIcon {
  color: var(--color-success);
}

.unlockedSkill::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: 0 0 15px var(--color-success-muted);
  opacity: 0.5;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.1); opacity: 0.25; }
  100% { transform: scale(1); opacity: 0.5; }
}

.availableSkill {
  background-color: var(--color-info-muted);
  border: 2px solid var(--color-info);
  color: var(--color-info);
}

.availableSkill .skillNodeIcon {
  color: var(--color-info);
}

.lockedSkill {
  background-color: var(--color-bg-secondary);
  border: 2px solid var(--color-text-disabled);
  color: var(--color-text-disabled);
  cursor: default;
}

.lockedSkill:hover {
  transform: none;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.selectedSkill {
  box-shadow: 0 0 20px rgba(0, 240, 255, 0.6);
  transform: scale(1.1);
}

/* Connection lines */
.connectionLine {
  position: absolute;
  background-color: var(--color-text-disabled);
  height: 2px;
  z-index: 1;
}

.unlockedConnection {
  background-color: var(--color-success);
  box-shadow: 0 0 8px var(--color-success-muted);
}

.partiallyUnlockedConnection {
  background: linear-gradient(to right, 
    var(--color-success) 0%, 
    var(--color-success) 50%, 
    var(--color-text-disabled) 50%, 
    var(--color-text-disabled) 100%
  );
}

/* Details container */
.detailsContainer {
  width: 300px;
  flex-shrink: 0;
  overflow-y: auto;
  padding-right: var(--space-xs);
}

/* Skill details panel */
.skillDetailsPanel {
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-sm);
  padding: var(--space-md);
  border: 1px solid var(--color-border-primary);
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.skillDetailHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-sm);
  margin-bottom: var(--space-xs);
}

.skillDetailTitle {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.skillName {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--color-text-primary);
}

.skillStatus {
  flex-shrink: 0;
}

.skillDescription {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: 1.5;
}

.skillDetailSection {
  margin-top: var(--space-sm);
  padding-top: var(--space-sm);
  border-top: 1px solid var(--color-border-primary);
}

.sectionTitle {
  margin: 0 0 var(--space-xs);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-text-primary);
}

/* Requirements list */
.requirementsList {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.requirementItem {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.requirementIcon {
  color: var(--color-text-disabled);
}

.metRequirement {
  color: var(--color-success);
}

.metRequirement .requirementIcon {
  color: var(--color-success);
}

.noRequirements {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Bonus list */
.bonusList {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.bonusItem {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
}

.bonusIcon {
  color: var(--color-accent-primary);
}

.bonusName {
  flex: 1;
  color: var(--color-text-secondary);
}

.bonusValue {
  color: var(--color-success);
  font-weight: 600;
  font-family: var(--font-mono);
}

/* Unlocks list */
.unlocksList {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.unlockItem {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: color 0.2s ease;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
}

.unlockItem:hover {
  color: var(--color-text-primary);
  background-color: rgba(0, 0, 0, 0.1);
}

.unlockIcon {
  color: var(--color-accent-primary);
}

.noUnlocks {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Unlock button */
.unlockButtonContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-md);
}

.costIndicator {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
}

.costIcon {
  color: var(--color-text-secondary);
}

.costValue {
  font-family: var(--font-mono);
  font-weight: 600;
  color: var(--color-text-secondary);
}

.affordableCost {
  color: var(--color-success);
}

.unaffordableCost {
  color: var(--color-error);
}

/* No selection message */
.noSelectionMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  padding: var(--space-md);
}

.noSelectionIcon {
  color: var(--color-text-disabled);
  margin-bottom: var(--space-sm);
  opacity: 0.7;
}

/* Scrollbars */
.detailsContainer::-webkit-scrollbar,
.treeContainer::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.detailsContainer::-webkit-scrollbar-track,
.treeContainer::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.detailsContainer::-webkit-scrollbar-thumb,
.treeContainer::-webkit-scrollbar-thumb {
  background: var(--color-accent-primary);
  border-radius: 3px;
}

.detailsContainer::-webkit-scrollbar-thumb:hover,
.treeContainer::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-primary-hover);
}

/* Responsive design */
@media (max-width: 768px) {
  .skillTreeContent {
    flex-direction: column;
  }
  
  .treeContainer {
    height: 450px;
  }
  
  .detailsContainer {
    width: 100%;
  }
  
  .categoryFilters {
    display: none;
  }
}