import React, { useState } from 'react';
import styles from './MarketplaceBrowser.module.css';
import { IconShoppingCart, IconSearch } from '@tabler/icons-react';
import clsx from 'clsx';

interface MarketplaceBrowserProps {
  className?: string;
}

interface MarketItem {
  id: string;
  name: string;
  description: string;
  price: number;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  image?: string;
}

const MarketplaceBrowser: React.FC<MarketplaceBrowserProps> = ({ className }) => {
  const [activeCategory, setActiveCategory] = useState<string>('public');
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  // Mock data for marketplace categories
  const categories = [
    { id: 'public', name: 'Public Market' },
    { id: 'gray', name: 'Gray Market' },
    { id: 'dark', name: 'Dark Market' },
  ];
  
  // Mock data for marketplace items
  const marketItems: Record<string, MarketItem[]> = {
    public: [
      { id: 'item-1', name: 'Basic Firewall', description: 'Entry-level protection against common threats', price: 500, rarity: 'common' },
      { id: 'item-2', name: 'Network Scanner', description: 'Identifies vulnerabilities in target networks', price: 750, rarity: 'common' },
    ],
    gray: [
      { id: 'item-3', name: 'Advanced Encryption', description: 'Military-grade encryption for your data', price: 2000, rarity: 'rare' },
      { id: 'item-4', name: 'Trace Cleaner', description: 'Removes digital footprints after operations', price: 1500, rarity: 'uncommon' },
    ],
    dark: [
      { id: 'item-5', name: 'Zero-Day Exploit', description: 'Undisclosed vulnerability with no available patches', price: 5000, rarity: 'epic' },
      { id: 'item-6', name: 'Backdoor Implant', description: 'Persistent access to compromised systems', price: 3500, rarity: 'rare' },
    ],
  };
  
  // Get items for the active category
  const activeItems = marketItems[activeCategory] || [];
  
  // Filter items based on search query
  const filteredItems = searchQuery
    ? activeItems.filter(item => 
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : activeItems;
  
  return (
    <div className={clsx(styles.marketplaceBrowser, className)}>
      <div className={styles.header}>
        <h1 className={styles.title}>Marketplace</h1>
        <div className="search-bar">
          {/* Search functionality would go here */}
        </div>
      </div>
      
      <div className={styles.content}>
        <div className={styles.categories}>
          {categories.map(category => (
            <div 
              key={category.id}
              className={clsx(
                styles.category,
                activeCategory === category.id && styles.active
              )}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </div>
          ))}
        </div>
        
        <div className={styles.items}>
          {filteredItems.length === 0 ? (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>
                <IconShoppingCart size={48} stroke={1} />
              </div>
              <div className={styles.emptyText}>
                No items available in this marketplace.
              </div>
            </div>
          ) : (
            <div className={styles.itemGrid}>
              {filteredItems.map(item => (
                <div key={item.id} className={styles.itemCard}>
                  <div className={styles.itemImage} style={item.image ? { backgroundImage: `url(${item.image})` } : undefined}></div>
                  <div className={styles.itemContent}>
                    <h3 className={styles.itemTitle}>{item.name}</h3>
                    <p className={styles.itemDescription}>{item.description}</p>
                    <div className={styles.itemFooter}>
                      <span className={styles.itemPrice}>{item.price} ¢</span>
                      <span className={clsx(styles.itemRarity, styles[`rarity-${item.rarity}`])}>
                        {item.rarity}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketplaceBrowser;