/**
 * Specialist feature selectors
 */

import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../../app/store';

// Basic selectors
export const selectSpecialistState = (state: RootState) => state.specialist;
export const selectSpecialists = (state: RootState) => state.specialist.specialists;
export const selectAvailableSpecialistIds = (state: RootState) => state.specialist.availableSpecialists;
export const selectHiredSpecialistIds = (state: RootState) => state.specialist.hiredSpecialists;
export const selectTeams = (state: RootState) => state.specialist.teams;
export const selectTrainingPrograms = (state: RootState) => state.specialist.trainingPrograms;
export const selectActiveSpecialistId = (state: RootState) => state.specialist.activeSpecialistId;
export const selectActiveTeamId = (state: RootState) => state.specialist.activeTeamId;
export const selectIsLoading = (state: RootState) => state.specialist.isLoading;
export const selectError = (state: RootState) => state.specialist.error;

// Derived selectors
export const selectAvailableSpecialists = createSelector(
  [selectSpecialists, selectAvailableSpecialistIds],
  (specialists, availableIds) => availableIds.map(id => specialists[id]).filter(Boolean)
);

export const selectHiredSpecialists = createSelector(
  [selectSpecialists, selectHiredSpecialistIds],
  (specialists, hiredIds) => hiredIds.map(id => specialists[id]).filter(Boolean)
);

export const selectActiveSpecialist = createSelector(
  [selectSpecialists, selectActiveSpecialistId],
  (specialists, activeSpecialistId) => activeSpecialistId ? specialists[activeSpecialistId] : null
);

export const selectActiveTeam = createSelector(
  [selectTeams, selectActiveTeamId],
  (teams, activeTeamId) => activeTeamId ? teams[activeTeamId] : null
);

export const selectSpecialistById = (id: string) => 
  createSelector(
    [selectSpecialists],
    (specialists) => specialists[id] || null
  );

export const selectTeamById = (id: string) => 
  createSelector(
    [selectTeams],
    (teams) => teams[id] || null
  );
