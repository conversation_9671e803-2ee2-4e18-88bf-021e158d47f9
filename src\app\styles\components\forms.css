/* Forms */
input, textarea, select {
  font-family: var(--font-main);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: 0.5em 0.75em;
  color: var(--text-primary);
  transition: all var(--transition-fast);
  width: 100%;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--border-active);
  box-shadow: var(--shadow-md);
}

textarea {
  resize: vertical;
  min-height: 80px;
}

/* Checkboxes and Radio buttons */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-sm);
  cursor: pointer;
}

.form-check input[type="checkbox"],
.form-check input[type="radio"] {
  width: auto;
  margin-right: var(--space-sm);
  accent-color: var(--neon-cyan);
}

/* Form groups */
.form-group {
  margin-bottom: var(--space-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-xs);
  font-weight: 500;
}

/* Form validation */
.form-error {
  color: var(--neon-pink);
  font-size: var(--font-size-sm);
  margin-top: var(--space-xs);
}

input.error, textarea.error, select.error {
  border-color: var(--neon-pink);
  box-shadow: 0 0 0 1px var(--neon-pink-muted);
}

/* Custom select styling */
select {
  appearance: none;
  padding-right: 2.5em;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2300f0ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.7em center;
  background-size: 1em;
}

/* Placeholder styling */
::placeholder {
  color: var(--text-muted);
  opacity: 1;
}
