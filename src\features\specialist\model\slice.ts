/**
 * Specialist slice for Redux state management
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  SpecialistState,
  FetchSpecialistsPayload,
  HireSpecialistPayload,
  FireSpecialistPayload,
  AssignToMissionPayload,
  ReturnFromMissionPayload,
  StartTrainingPayload,
  CompleteTrainingPayload,
  CreateTeamPayload,
  UpdateTeamPayload
} from './types';
import {
  Specialist,
  Team,
  TrainingProgram
} from '@/core/specialist/types';
// Import service when available
// import { specialistService } from '@/core/specialist';

// Initial state
const initialState: SpecialistState = {
  specialists: {},
  availableSpecialists: [],
  hiredSpecialists: [],
  teams: {},
  trainingPrograms: {},
  activeSpecialistId: null,
  activeTeamId: null,
  isLoading: false,
  error: null
};

// This is a mock implementation until we have the actual service
const getMockSpecialists = (count: number): Specialist[] => {
  const specialists: Specialist[] = [];
  
  for (let i = 0; i < count; i++) {
    specialists.push({
      id: `specialist-${i}`,
      name: `Test Specialist ${i}`,
      specialization: 'malware_developer',
      skills: {
        hacking: 70,
        cryptography: 60,
        malware_development: 80,
        network_infiltration: 65,
        exploitation: 75,
        social_engineering: 40,
        persuasion: 30,
        deception: 50,
        intel_analysis: 60,
        osint: 70,
        threat_hunting: 65,
        physical_security: 20,
        surveillance: 30,
        counter_surveillance: 25
      },
      traits: [
        {
          type: 'meticulous',
          name: 'Meticulous',
          description: 'Pays extreme attention to detail',
          effect: 'Reduces chance of detection',
          isPositive: true
        }
      ],
      attributes: {
        reliability: 80,
        discretion: 75,
        efficiency: 70,
        loyalty: 85
      },
      background: 'Former security researcher with a focus on malware analysis',
      cost: 5000,
      status: i % 2 === 0 ? 'available' : 'hired',
      level: 2,
      experience: 750
    });
  }
  
  return specialists;
};

// Async thunks - using mocks for now
export const fetchSpecialists = createAsyncThunk(
  'specialist/fetchSpecialists',
  async ({ status = 'all' }: FetchSpecialistsPayload, { rejectWithValue }) => {
    try {
      // This will be replaced with actual service calls
      // const specialists = await specialistService.getAvailableSpecialists();
      const mockSpecialists = getMockSpecialists(5);
      
      if (status === 'all') {
        return mockSpecialists;
      }
      
      return mockSpecialists.filter(spec => spec.status === status);
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch specialists');
    }
  }
);

export const hireSpecialist = createAsyncThunk(
  'specialist/hireSpecialist',
  async ({ specialistId }: HireSpecialistPayload, { rejectWithValue }) => {
    try {
      // This will be replaced with actual service calls
      // const specialist = await specialistService.hireSpecialist(specialistId);
      const mockSpecialists = getMockSpecialists(5);
      const specialist = mockSpecialists.find(s => s.id === specialistId);
      
      if (!specialist) {
        throw new Error(`Specialist with ID ${specialistId} not found`);
      }
      
      return { ...specialist, status: 'hired' as const };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to hire specialist');
    }
  }
);

export const fireSpecialist = createAsyncThunk(
  'specialist/fireSpecialist',
  async ({ specialistId }: FireSpecialistPayload, { rejectWithValue }) => {
    try {
      // This will be replaced with actual service calls
      // const result = await specialistService.fireSpecialist(specialistId);
      return specialistId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fire specialist');
    }
  }
);

// Specialist slice
export const specialistSlice = createSlice({
  name: 'specialist',
  initialState,
  reducers: {
    setActiveSpecialist: (state, action: PayloadAction<string | null>) => {
      state.activeSpecialistId = action.payload;
    },
    setActiveTeam: (state, action: PayloadAction<string | null>) => {
      state.activeTeamId = action.payload;
    },
    resetSpecialistState: (state) => {
      return initialState;
    }
  },
  extraReducers: (builder) => {
    // Fetch specialists
    builder.addCase(fetchSpecialists.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fetchSpecialists.fulfilled, (state, action: PayloadAction<Specialist[]>) => {
      state.isLoading = false;
      
      // Update specialists record
      action.payload.forEach(specialist => {
        state.specialists[specialist.id] = specialist;
      });
      
      // Update specialist lists by status
      state.availableSpecialists = action.payload
        .filter(specialist => specialist.status === 'available')
        .map(specialist => specialist.id);
      
      state.hiredSpecialists = action.payload
        .filter(specialist => specialist.status === 'hired' || 
                              specialist.status === 'on_mission' || 
                              specialist.status === 'training')
        .map(specialist => specialist.id);
    });
    builder.addCase(fetchSpecialists.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string || 'Failed to fetch specialists';
    });
    
    // Hire specialist
    builder.addCase(hireSpecialist.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(hireSpecialist.fulfilled, (state, action: PayloadAction<Specialist>) => {
      state.isLoading = false;
      
      const specialist = action.payload;
      
      // Update specialist in store
      state.specialists[specialist.id] = specialist;
      
      // Move from available to hired
      state.availableSpecialists = state.availableSpecialists.filter(id => id !== specialist.id);
      
      if (!state.hiredSpecialists.includes(specialist.id)) {
        state.hiredSpecialists.push(specialist.id);
      }
    });
    builder.addCase(hireSpecialist.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string || 'Failed to hire specialist';
    });
    
    // Fire specialist
    builder.addCase(fireSpecialist.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(fireSpecialist.fulfilled, (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      
      const specialistId = action.payload;
      
      // Move from hired to available
      state.hiredSpecialists = state.hiredSpecialists.filter(id => id !== specialistId);
      
      // Update specialist status
      if (state.specialists[specialistId]) {
        state.specialists[specialistId].status = 'available';
      }
      
      // Add to available list
      if (!state.availableSpecialists.includes(specialistId)) {
        state.availableSpecialists.push(specialistId);
      }
    });
    builder.addCase(fireSpecialist.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string || 'Failed to fire specialist';
    });
  }
});

export const { setActiveSpecialist, setActiveTeam, resetSpecialistState } = specialistSlice.actions;

export default specialistSlice.reducer;
