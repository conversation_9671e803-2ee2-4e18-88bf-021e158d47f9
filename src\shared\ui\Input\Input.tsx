import React, { forwardRef } from 'react';
import styles from './Input.module.css';
import clsx from 'clsx';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'flushed' | 'unstyled';
  isFullWidth?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>((
  {
    label,
    error,
    hint,
    leftIcon,
    rightIcon,
    size = 'md',
    variant = 'default',
    isFullWidth = false,
    className,
    disabled,
    required,
    ...props
  },
  ref
) => {
  const inputGroupClasses = clsx(
    styles.inputGroup,
    isFullWidth && styles.fullWidth,
    disabled && styles.disabled,
    error && styles.hasError,
    className
  );

  const inputWrapperClasses = clsx(
    styles.inputWrapper,
    styles[`variant-${variant}`],
    styles[`size-${size}`],
    leftIcon && styles.hasLeftIcon,
    rightIcon && styles.hasRightIcon
  );

  const inputClasses = clsx(
    styles.input
  );

  return (
    <div className={inputGroupClasses}>
      {label && (
        <div className={styles.labelWrapper}>
          <label className={styles.label}>
            {label}
            {required && <span className={styles.required}>*</span>}
          </label>
        </div>
      )}

      <div className={inputWrapperClasses}>
        {leftIcon && <div className={styles.leftIcon}>{leftIcon}</div>}
        <input
          ref={ref}
          className={inputClasses}
          disabled={disabled}
          required={required}
          aria-invalid={error ? 'true' : 'false'}
          {...props}
        />
        {rightIcon && <div className={styles.rightIcon}>{rightIcon}</div>}
      </div>

      {(error || hint) && (
        <div className={styles.feedbackWrapper}>
          {error ? (
            <div className={styles.errorMessage}>{error}</div>
          ) : hint ? (
            <div className={styles.hint}>{hint}</div>
          ) : null}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;