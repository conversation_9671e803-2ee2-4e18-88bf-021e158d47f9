// Core market service interfaces
import { Item, Market, MarketType, Requirement, Vendor } from './types';

/**
 * Repository interface for accessing market data
 */
export interface MarketRepository {
  /**
   * Get all available markets
   */
  getAllMarkets(): Promise<Market[]>;
  
  /**
   * Get a market by ID
   */
  getMarketById(id: string): Promise<Market | null>;
  
  /**
   * Get markets by type
   */
  getMarketsByType(type: MarketType): Promise<Market[]>;
  
  /**
   * Get markets accessible to the player based on requirements
   */
  getAccessibleMarkets(): Promise<Market[]>;
  
  /**
   * Refresh market data (triggered by in-game time passage)
   */
  refreshMarkets(): Promise<void>;
}

/**
 * Repository interface for accessing vendor data
 */
export interface VendorRepository {
  /**
   * Get all vendors in a specific market
   */
  getVendorsByMarket(marketId: string): Promise<Vendor[]>;
  
  /**
   * Get a vendor by ID
   */
  getVendorById(id: string): Promise<Vendor | null>;
  
  /**
   * Get vendors accessible to the player based on requirements
   */
  getAccessibleVendors(marketId: string): Promise<Vendor[]>;
  
  /**
   * Update vendor relationship level
   */
  updateVendorRelationship(vendorId: string, newLevel: number): Promise<void>;
}

/**
 * Repository interface for accessing item data
 */
export interface ItemRepository {
  /**
   * Get all items from a specific vendor
   */
  getItemsByVendor(vendorId: string): Promise<Item[]>;
  
  /**
   * Get an item by ID
   */
  getItemById(id: string): Promise<Item | null>;
  
  /**
   * Get items that match certain criteria
   */
  searchItems(criteria: Partial<Item>): Promise<Item[]>;
  
  /**
   * Update item availability or properties
   */
  updateItem(id: string, updates: Partial<Item>): Promise<void>;
}

/**
 * Repository interface for accessing player inventory
 */
export interface InventoryRepository {
  /**
   * Get all items in player's inventory
   */
  getPlayerInventory(): Promise<Item[]>;
  
  /**
   * Add an item to player's inventory
   */
  addToInventory(item: Item): Promise<void>;
  
  /**
   * Remove an item from player's inventory
   */
  removeFromInventory(itemId: string): Promise<void>;
  
  /**
   * Get player's current resources (currency, etc.)
   */
  getPlayerResources(): Promise<Record<string, number>>;
  
  /**
   * Update player's resources
   */
  updatePlayerResources(updates: Record<string, number>): Promise<void>;
}

/**
 * Market Service interface for handling marketplace operations
 */
export interface MarketService {
  /**
   * Check if player meets requirements to access a market
   */
  checkMarketAccess(marketId: string): Promise<boolean>;
  
  /**
   * Process a purchase transaction
   */
  purchaseItem(itemId: string): Promise<boolean>;
  
  /**
   * Sell an item from player's inventory
   */
  sellItem(itemId: string): Promise<boolean>;
  
  /**
   * Get available items that match player's search criteria
   */
  searchForItems(searchText: string, filters?: Partial<Item>): Promise<Item[]>;
  
  /**
   * Check if player meets requirements for an item
   */
  checkItemRequirements(itemId: string): Promise<boolean>;
  
  /**
   * Refresh market offerings (triggered by game time or events)
   */
  refreshMarketOfferings(): Promise<void>;
  
  /**
   * Get special deals or discounted items
   */
  getSpecialOffers(): Promise<Item[]>;
}
