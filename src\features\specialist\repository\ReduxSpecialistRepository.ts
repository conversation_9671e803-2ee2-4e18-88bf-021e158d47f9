/**
 * Redux-backed repository implementation for specialists
 */

import { store } from '../../../app/store';
import { SpecialistRepository } from '@/core/specialist/interfaces';
import { Specialist } from '@/core/specialist/types';
import { fetchSpecialists, hireSpecialist, fireSpecialist } from '../model/slice';
import { selectAvailableSpecialists, selectHiredSpecialists, selectSpecialistById } from '../model/selectors';

/**
 * Redux-backed implementation of the specialist repository
 * This connects the core domain layer to the Redux state management
 */
export class ReduxSpecialistRepository implements SpecialistRepository {
  async getAvailableSpecialists(): Promise<Specialist[]> {
    // Dispatch the fetch action
    await store.dispatch(fetchSpecialists({ status: 'available' }));
    
    // Return the current state after dispatching
    const state = store.getState();
    return selectAvailableSpecialists(state);
  }
  
  async getHiredSpecialists(): Promise<Specialist[]> {
    // Dispatch the fetch action
    await store.dispatch(fetchSpecialists({ status: 'hired' }));
    
    // Return the current state after dispatching
    const state = store.getState();
    return selectHiredSpecialists(state);
  }
  
  async getSpecialistById(id: string): Promise<Specialist | null> {
    // Get the current state
    const state = store.getState();
    let specialist = selectSpecialistById(id)(state);
    
    // If specialist not found, try fetching all specialists
    if (!specialist) {
      await store.dispatch(fetchSpecialists({ status: 'all' }));
      const updatedState = store.getState();
      specialist = selectSpecialistById(id)(updatedState);
    }
    
    return specialist;
  }
  
  async hireSpecialist(id: string): Promise<Specialist> {
    const result = await store.dispatch(hireSpecialist({ specialistId: id }));
    
    if (result.meta.requestStatus === 'rejected') {
      throw new Error(result.payload as string || 'Failed to hire specialist');
    }
    
    return result.payload as Specialist;
  }
  
  async fireSpecialist(id: string): Promise<boolean> {
    const result = await store.dispatch(fireSpecialist({ specialistId: id }));
    
    if (result.meta.requestStatus === 'rejected') {
      throw new Error(result.payload as string || 'Failed to fire specialist');
    }
    
    return true;
  }
  
  async updateSpecialist(id: string, updates: Partial<Specialist>): Promise<Specialist> {
    // In a real implementation, we would dispatch an update action
    // For now, we'll throw an error since we don't have that action yet
    throw new Error('Not implemented yet');
  }
  
  async generateSpecialists(count: number): Promise<Specialist[]> {
    // In a real implementation, we would dispatch an action to generate specialists
    // For now, we'll throw an error since we don't have that action yet
    throw new Error('Not implemented yet');
  }
}
