import { Command, CommandResult, CommandContext } from '../types';

const clearCommand: Command = {
  help: 'Clears the terminal screen.\nUsage: clear',
  
  execute(args: string[], context: CommandContext): CommandResult {
    // Clear is handled differently - we return empty output
    // and the actual clearing is handled at the component level
    return { 
      output: '', 
      status: 'success' 
    };
  }
};

export { clearCommand };
