# Widgets Layer

This layer contains composite UI components that combine multiple features or implement application-wide interfaces.

## Structure

Widgets are organized by their purpose and follow this structure:

```
widgets/
├── [widget-name]/      # Composite UI component
│   ├── ui/             # UI components
│   ├── model/          # Widget-specific state (optional)
│   ├── lib/            # Widget-specific utilities (optional)
│   └── index.ts        # Public API
```

## Widget Types

### App-Level Widgets

- **app-shell** - Main application container structure
- **top-bar** - Header bar with configurable widgets
- **bottom-bar** - Application launcher
- **tab-bar** - Tab navigation
- **tab-content** - Tab content manager
- **notifications** - Notification system

### Feature-Composite Widgets

- **mission-dashboard** - Combines mission-related features
- **specialist-manager** - Combines specialist-related features
- **terminal-console** - Terminal application
- **marketplace-browser** - Marketplace application
- **training-center** - Training and skill development

## Widget Principles

1. **Composition Over Implementation**: Widgets should compose feature-level components rather than implement business logic
2. **Feature-Agnostic State**: Widget state should only relate to UI concerns, not business logic
3. **Simple API**: Widgets should have simple, well-defined props
4. **Clear Boundaries**: Widgets should have clear responsibilities and minimal dependencies
