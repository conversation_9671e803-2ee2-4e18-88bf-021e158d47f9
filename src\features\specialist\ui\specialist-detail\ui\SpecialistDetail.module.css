.specialistDetail {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  border-radius: var(--border-radius-md);
  position: relative;
}

.specialistDetail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(0, 240, 255, 0),
    rgba(0, 240, 255, 0.5),
    rgba(0, 240, 255, 0)
  );
  z-index: 1;
}

/* Header Styles */
.headerTitleContainer {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.backButton {
  margin-right: var(--space-sm);
}

.headerSubtitle {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.actions {
  display: flex;
  gap: var(--space-sm);
}

/* Content Styles */
.content {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  overflow-y: auto;
  padding: var(--space-md) var(--space-md) var(--space-lg);
}

/* Profile Header */
.profileHeader {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
  padding: var(--space-md);
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border-primary);
  position: relative;
  overflow: hidden;
}

.profileHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at top right, rgba(0, 240, 255, 0.05), transparent 70%);
  pointer-events: none;
}

.avatarSection {
  position: relative;
  flex-shrink: 0;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--color-accent-primary);
  color: var(--color-text-on-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: 600;
  border: 3px solid var(--color-bg-secondary);
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.3);
  overflow: hidden;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.initialsAvatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.levelBadge {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--color-accent-secondary);
  color: var(--color-text-on-accent);
  font-size: var(--font-size-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.levelIcon {
  color: var(--color-warning);
}

.profileInfo {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  flex: 1;
}

.profileInfoRow {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
}

.infoIcon {
  color: var(--color-accent-primary);
  flex-shrink: 0;
}

.infoLabel {
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
  font-weight: 500;
  min-width: 110px;
  flex-shrink: 0;
}

.infoValue {
  color: var(--color-text-primary);
}

/* Grid Layout */
.detailsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
}

.section {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.sectionIcon {
  color: var(--color-accent-primary);
  margin-right: var(--space-xs);
}

.skillsSection {
  grid-column: 1;
  grid-row: 1 / span 2;
}

.attributesSection {
  grid-column: 2;
  grid-row: 1;
}

.traitsSection {
  grid-column: 2;
  grid-row: 2;
}

.historySection {
  grid-column: 1 / span 2;
  grid-row: 3;
}

.backgroundSection {
  grid-column: 1 / span 2;
  margin-top: var(--space-md);
}

.backgroundText {
  white-space: pre-line;
  line-height: 1.6;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* Skills Section */
.skillList {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.skillItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skillHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skillName {
  font-family: var(--font-mono);
  font-weight: 500;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
}

.skillValue {
  font-weight: 600;
  color: var(--color-accent-primary);
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
}

.skillBar {
  height: 6px;
  background-color: var(--color-bg-tertiary);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.skillProgress {
  height: 100%;
  background: linear-gradient(90deg, var(--color-accent-primary), var(--color-accent-primary-hover));
  border-radius: 3px;
  position: relative;
  box-shadow: 0 0 8px rgba(0, 240, 255, 0.3);
}

.skillProgress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Attributes Section */
.attributeList {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-sm);
}

.attributeItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-sm);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-border-primary);
  position: relative;
  overflow: hidden;
}

.attributeItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(0, 240, 255, 0.05), transparent 70%);
  pointer-events: none;
}

.attributeValue {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-accent-primary);
  margin-bottom: 4px;
  text-shadow: 0 0 8px rgba(0, 240, 255, 0.3);
}

.attributeName {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  text-align: center;
  font-family: var(--font-mono);
}

/* Traits Section */
.traitList {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.traitItem {
  display: flex;
  gap: var(--space-sm);
  padding: var(--space-sm);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-border-primary);
  position: relative;
  overflow: hidden;
}

.traitPositive {
  border-left: 3px solid var(--color-success);
}

.traitNegative {
  border-left: 3px solid var(--color-error);
}

.traitIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.traitPositive .traitIcon {
  color: var(--color-success);
}

.traitNegative .traitIcon {
  color: var(--color-error);
}

.traitInfo {
  flex: 1;
}

.traitName {
  font-weight: 600;
  margin: 0 0 4px;
  font-size: var(--font-size-sm);
}

.traitNamePositive {
  color: var(--color-success);
}

.traitNameNegative {
  color: var(--color-error);
}

.traitDescription {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* History Section */
.historyList {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.historyHeader {
  display: grid;
  grid-template-columns: 140px 1fr 120px 100px;
  padding: 0 var(--space-sm) var(--space-xs);
  border-bottom: 1px solid var(--color-border-primary);
  margin-bottom: var(--space-sm);
}

.historyHeaderItem {
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--color-text-secondary);
  text-transform: uppercase;
  font-family: var(--font-mono);
}

.historyItem {
  display: grid;
  grid-template-columns: 140px 1fr 120px 100px;
  padding: var(--space-sm);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-border-primary);
  align-items: center;
}

.historyDate {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
}

.historyMission {
  font-weight: 500;
  font-size: var(--font-size-sm);
}

.historyRole {
  font-size: var(--font-size-xs);
  color: var(--color-accent-primary);
  font-family: var(--font-mono);
}

.historyOutcome {
  justify-self: center;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg);
  color: var(--color-text-secondary);
  text-align: center;
}

.emptyIcon {
  color: var(--color-text-disabled);
  margin-bottom: var(--space-sm);
  opacity: 0.7;
}

.emptyState p {
  margin: 0;
  font-size: var(--font-size-sm);
  font-family: var(--font-mono);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .detailsGrid {
    grid-template-columns: 1fr;
  }
  
  .skillsSection,
  .attributesSection,
  .traitsSection,
  .historySection,
  .backgroundSection {
    grid-column: 1;
  }
  
  .skillsSection {
    grid-row: 1;
  }
  
  .attributesSection {
    grid-row: 2;
  }
  
  .traitsSection {
    grid-row: 3;
  }
  
  .historySection {
    grid-row: 4;
  }
  
  .profileHeader {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .profileInfo {
    align-items: center;
  }
  
  .historyHeader,
  .historyItem {
    grid-template-columns: 100px 1fr 80px;
  }
  
  .historyRole {
    display: none;
  }
}