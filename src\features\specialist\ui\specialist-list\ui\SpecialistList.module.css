/* SpecialistList styles with cyberpunk theming */

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Filters section */
.filtersSection {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
  position: relative;
}

.searchWrapper {
  width: 100%;
  position: relative;
}

.searchWrapper::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(0, 240, 255, 0),
    rgba(0, 240, 255, 0.3),
    rgba(0, 240, 255, 0)
  );
}

.filterActions {
  display: flex;
  gap: var(--space-sm);
}

.dropdownIcon {
  transition: transform 0.2s ease;
}

.dropdownIconOpen {
  transform: rotate(180deg);
}

.activeFilters {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-top: var(--space-xs);
  flex-wrap: wrap;
  padding: var(--space-xs);
  border-radius: var(--border-radius-sm);
  background-color: rgba(0, 0, 0, 0.1);
}

.activeFiltersLabel {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
}

.filterBadges {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  flex-wrap: wrap;
  flex: 1;
}

.badgeContent {
  display: flex;
  align-items: center;
  gap: 4px;
}

.badgeLabel {
  font-weight: 500;
  opacity: 0.7;
}

.badgeIcon {
  margin-left: 2px;
  opacity: 0.7;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.badgeIcon:hover {
  opacity: 1;
}

/* Filter panel */
.filterPanel {
  margin-top: var(--space-xs);
  padding: var(--space-sm);
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-border-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.filterPanelSection {
  margin-bottom: var(--space-sm);
}

.filterPanelSection:last-child {
  margin-bottom: 0;
}

.filterPanelTitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xs);
  font-weight: 500;
  font-family: var(--font-mono);
}

.filterPanelOptions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
}

.filterOption {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-sm);
  background-color: rgba(0, 0, 0, 0.15);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.filterOption:hover {
  background-color: rgba(0, 240, 255, 0.1);
  color: var(--color-text-primary);
}

.filterOptionActive {
  background-color: rgba(0, 240, 255, 0.15);
  color: var(--color-accent-primary);
  border: 1px solid rgba(0, 240, 255, 0.3);
}

.filterOptionIcon {
  color: var(--color-accent-primary);
}

/* Specialist list */
.specialistList {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  width: 100%;
  overflow-y: auto;
  padding-right: var(--space-xs);
  padding-bottom: var(--space-md);
}

/* Scrollbar styling */
.specialistList::-webkit-scrollbar {
  width: 6px;
}

.specialistList::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.specialistList::-webkit-scrollbar-thumb {
  background: var(--color-accent-primary);
  border-radius: 3px;
}

.specialistList::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-primary-hover);
}

/* Specialist Card */
.specialistCard {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-sm);
  background-color: var(--color-bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--color-border-primary);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.specialistCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--color-accent-primary);
  transform: scaleY(0);
  transform-origin: bottom;
  transition: transform 0.3s ease;
}

.specialistCard:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-color: var(--color-accent-primary-hover);
}

.specialistCard:hover::before {
  transform: scaleY(1);
}

.specialistAvatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  border: 2px solid var(--color-border-primary);
  flex-shrink: 0;
  overflow: hidden;
  transition: border-color 0.2s ease;
}

.specialistCard:hover .specialistAvatar {
  border-color: var(--color-accent-primary);
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.3);
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.specialistInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  min-width: 0; /* Allows text truncation to work */
}

.specialistHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.specialistName {
  font-size: var(--font-size-md);
  font-weight: 600;
  margin: 0;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.specialistSubheader {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.specializationLabel {
  font-size: var(--font-size-xs);
  color: var(--color-accent-primary);
  font-family: var(--font-mono);
  font-weight: 500;
}

.levelBadge {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  background-color: var(--color-bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.specialistStats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm) var(--space-md);
  margin-top: 2px;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--font-size-xs);
}

.statIcon {
  color: var(--color-text-secondary);
}

.statLabel {
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
}

.statValue {
  color: var(--color-text-primary);
  font-weight: 500;
}

.viewDetailsButton {
  flex-shrink: 0;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.2s ease;
}

.specialistCard:hover .viewDetailsButton {
  opacity: 1;
  transform: translateX(0);
}

/* Loading state */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
  text-align: center;
  flex: 1;
}

.loadingSpinner {
  animation: spin 1.5s linear infinite;
  color: var(--color-accent-primary);
  margin-bottom: var(--space-md);
  filter: drop-shadow(0 0 5px rgba(0, 240, 255, 0.5));
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loadingText {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-family: var(--font-mono);
}

/* Empty state */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
  text-align: center;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-md);
  border: 1px dashed var(--color-border-primary);
  position: relative;
  overflow: hidden;
  min-height: 200px;
}

.emptyContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(0, 240, 255, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.emptyIcon {
  color: var(--color-text-disabled);
  margin-bottom: var(--space-md);
  opacity: 0.7;
}

.emptyMessage {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-md);
  font-family: var(--font-mono);
}

/* Responsive styling */
@media (max-width: 640px) {
  .specialistCard {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
  
  .specialistAvatar {
    margin-bottom: var(--space-xs);
  }
  
  .specialistHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }
  
  .viewDetailsButton {
    margin-top: var(--space-sm);
    opacity: 1;
    transform: none;
    align-self: flex-end;
  }
  
  .specialistCard:hover .viewDetailsButton {
    transform: none;
  }
}