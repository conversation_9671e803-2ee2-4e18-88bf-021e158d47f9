import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Specialist, Team, TrainingProgram } from '@/core/specialist/types';
import { SpecialistState } from './types';

interface SpecialistActions {
  // Async actions
  fetchSpecialists: (status?: 'available' | 'hired' | 'all') => Promise<void>;
  hireSpecialist: (specialistId: string) => Promise<void>;
  fireSpecialist: (specialistId: string) => Promise<void>;
  assignToMission: (specialistId: string, missionId: string) => Promise<void>;
  returnFromMission: (specialistId: string, missionId: string, outcome: 'success' | 'failure' | 'partial', notes?: string) => Promise<void>;
  startTraining: (specialistId: string, programId: string) => Promise<void>;
  completeTraining: (specialistId: string, programId: string) => Promise<void>;
  createTeam: (name: string, memberIds: string[]) => Promise<void>;
  updateTeam: (teamId: string, updates: Partial<Team>) => Promise<void>;
  
  // Sync actions
  setActiveSpecialist: (specialistId: string | null) => void;
  setActiveTeam: (teamId: string | null) => void;
  resetSpecialistState: () => void;
  
  // Internal state management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

type SpecialistStore = SpecialistState & SpecialistActions;

const initialState: SpecialistState = {
  specialists: {},
  availableSpecialists: [],
  hiredSpecialists: [],
  teams: {},
  trainingPrograms: {},
  activeSpecialistId: null,
  activeTeamId: null,
  isLoading: false,
  error: null
};

export const useSpecialistStore = create<SpecialistStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      fetchSpecialists: async (status?: 'available' | 'hired' | 'all') => {
        set({ isLoading: true, error: null });
        
        try {
          // Mock implementation - replace with actual service call
          const mockSpecialists: Specialist[] = [
            {
              id: '1',
              name: 'Alex Chen',
              specialization: 'Network Security',
              level: 3,
              experience: 1250,
              skills: ['Penetration Testing', 'Firewall Management', 'Network Analysis'],
              status: 'available',
              cost: 5000,
              availability: true,
              stats: {
                technical: 85,
                stealth: 70,
                social: 60,
                physical: 55
              }
            }
          ];
          
          // Organize specialists by status
          const specialistMap: Record<string, Specialist> = {};
          const availableSpecialists: string[] = [];
          const hiredSpecialists: string[] = [];
          
          mockSpecialists.forEach(specialist => {
            specialistMap[specialist.id] = specialist;
            
            if (specialist.status === 'available') {
              availableSpecialists.push(specialist.id);
            } else if (specialist.status === 'hired') {
              hiredSpecialists.push(specialist.id);
            }
          });
          
          set({
            specialists: specialistMap,
            availableSpecialists,
            hiredSpecialists,
            isLoading: false
          });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to fetch specialists' 
          });
        }
      },
      
      hireSpecialist: async (specialistId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const { specialists, availableSpecialists, hiredSpecialists } = get();
          const specialist = specialists[specialistId];
          
          if (!specialist) {
            throw new Error('Specialist not found');
          }
          
          // Update specialist status
          const updatedSpecialist = { ...specialist, status: 'hired' as const };
          
          // Move from available to hired
          const newAvailableSpecialists = availableSpecialists.filter(id => id !== specialistId);
          const newHiredSpecialists = [...hiredSpecialists, specialistId];
          
          set({
            specialists: {
              ...specialists,
              [specialistId]: updatedSpecialist
            },
            availableSpecialists: newAvailableSpecialists,
            hiredSpecialists: newHiredSpecialists,
            isLoading: false
          });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to hire specialist' 
          });
        }
      },
      
      fireSpecialist: async (specialistId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const { specialists, availableSpecialists, hiredSpecialists } = get();
          const specialist = specialists[specialistId];
          
          if (!specialist) {
            throw new Error('Specialist not found');
          }
          
          // Update specialist status
          const updatedSpecialist = { ...specialist, status: 'available' as const };
          
          // Move from hired to available
          const newHiredSpecialists = hiredSpecialists.filter(id => id !== specialistId);
          const newAvailableSpecialists = [...availableSpecialists, specialistId];
          
          set({
            specialists: {
              ...specialists,
              [specialistId]: updatedSpecialist
            },
            availableSpecialists: newAvailableSpecialists,
            hiredSpecialists: newHiredSpecialists,
            isLoading: false
          });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to fire specialist' 
          });
        }
      },
      
      assignToMission: async (specialistId: string, missionId: string) => {
        // Mock implementation
        set({ isLoading: true, error: null });
        
        try {
          // Implementation would go here
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to assign specialist to mission' 
          });
        }
      },
      
      returnFromMission: async (specialistId: string, missionId: string, outcome: 'success' | 'failure' | 'partial', notes?: string) => {
        // Mock implementation
        set({ isLoading: true, error: null });
        
        try {
          // Implementation would go here
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to return specialist from mission' 
          });
        }
      },
      
      startTraining: async (specialistId: string, programId: string) => {
        // Mock implementation
        set({ isLoading: true, error: null });
        
        try {
          // Implementation would go here
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to start training' 
          });
        }
      },
      
      completeTraining: async (specialistId: string, programId: string) => {
        // Mock implementation
        set({ isLoading: true, error: null });
        
        try {
          // Implementation would go here
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to complete training' 
          });
        }
      },
      
      createTeam: async (name: string, memberIds: string[]) => {
        // Mock implementation
        set({ isLoading: true, error: null });
        
        try {
          // Implementation would go here
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to create team' 
          });
        }
      },
      
      updateTeam: async (teamId: string, updates: Partial<Team>) => {
        // Mock implementation
        set({ isLoading: true, error: null });
        
        try {
          // Implementation would go here
          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to update team' 
          });
        }
      },
      
      setActiveSpecialist: (specialistId: string | null) => {
        set({ activeSpecialistId: specialistId });
      },
      
      setActiveTeam: (teamId: string | null) => {
        set({ activeTeamId: teamId });
      },
      
      resetSpecialistState: () => {
        set(initialState);
      },
      
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
      
      setError: (error: string | null) => {
        set({ error });
      },
    }),
    {
      name: 'specialist-store',
    }
  )
);

// Selectors for easier access to derived state
export const useSpecialistSelectors = () => {
  const store = useSpecialistStore();

  return {
    // Basic selectors
    specialists: store.specialists,
    availableSpecialists: store.availableSpecialists.map(id => store.specialists[id]).filter(Boolean),
    hiredSpecialists: store.hiredSpecialists.map(id => store.specialists[id]).filter(Boolean),
    teams: store.teams,
    trainingPrograms: store.trainingPrograms,
    activeSpecialist: store.activeSpecialistId ? store.specialists[store.activeSpecialistId] : null,
    activeTeam: store.activeTeamId ? store.teams[store.activeTeamId] : null,
    isLoading: store.isLoading,
    error: store.error,

    // Helper functions
    getSpecialistById: (id: string) => store.specialists[id] || null,
    getTeamById: (id: string) => store.teams[id] || null,
  };
};
