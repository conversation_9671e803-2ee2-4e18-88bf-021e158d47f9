// Mission module exports
import { MissionService } from './MissionService';
import { IMissionService } from './interfaces';
import { MockMissionRepository } from '../../features/mission/repository';

// Create repository
const missionRepository = new MockMissionRepository();

// Create and export mission service
export const missionService: IMissionService = new MissionService(
  missionRepository
);

// Export types
export * from './types';
export * from './interfaces';
