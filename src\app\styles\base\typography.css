/* Typography styles */

body {
  font-family: var(--font-main);
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 600;
  margin: 0 0 0.5em 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  line-height: var(--line-height-tight);
}

h1 {
  font-size: var(--font-size-4xl);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

h2 {
  font-size: var(--font-size-3xl);
  color: var(--text-accent);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 1em 0;
}

/* Links */
a {
  color: var(--text-accent);
  text-decoration: none;
  transition: color var(--transition-fast), text-shadow var(--transition-fast);
  position: relative;
}

a:hover {
  color: var(--neon-cyan);
  text-shadow: 0 0 8px rgba(0, 240, 255, 0.5);
}

a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform var(--transition-normal);
}

a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Monospace text */
code, pre, .monospace {
  font-family: var(--font-mono);
  font-size: 0.9em;
}

/* Terminal text */
.terminal-text {
  font-family: var(--font-mono);
  color: var(--neon-cyan);
  background-color: var(--cyberpunk-black);
  padding: 0.2em 0.4em;
  border-radius: var(--border-radius-sm);
  text-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
}
