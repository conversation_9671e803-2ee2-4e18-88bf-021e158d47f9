import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../../app/store';

// Basic selectors
export const selectMissionState = (state: RootState) => state.mission;
export const selectMissions = (state: RootState) => state.mission.missions;
export const selectAvailableMissionIds = (state: RootState) => state.mission.availableMissions;
export const selectInProgressMissionIds = (state: RootState) => state.mission.inProgressMissions;
export const selectCompletedMissionIds = (state: RootState) => state.mission.completedMissions;
export const selectFailedMissionIds = (state: RootState) => state.mission.failedMissions;
export const selectActiveMissionId = (state: RootState) => state.mission.activeMissionId;
export const selectMissionProgress = (state: RootState) => state.mission.missionProgress;
export const selectIsLoading = (state: RootState) => state.mission.isLoading;
export const selectError = (state: RootState) => state.mission.error;

// Derived selectors
export const selectAvailableMissions = createSelector(
  [selectMissions, selectAvailableMissionIds],
  (missions, availableIds) => availableIds.map(id => missions[id]).filter(Boolean)
);

export const selectInProgressMissions = createSelector(
  [selectMissions, selectInProgressMissionIds],
  (missions, inProgressIds) => inProgressIds.map(id => missions[id]).filter(Boolean)
);

export const selectCompletedMissions = createSelector(
  [selectMissions, selectCompletedMissionIds],
  (missions, completedIds) => completedIds.map(id => missions[id]).filter(Boolean)
);

export const selectFailedMissions = createSelector(
  [selectMissions, selectFailedMissionIds],
  (missions, failedIds) => failedIds.map(id => missions[id]).filter(Boolean)
);

export const selectActiveMission = createSelector(
  [selectMissions, selectActiveMissionId],
  (missions, activeMissionId) => activeMissionId ? missions[activeMissionId] : null
);

export const selectActiveMissionProgress = createSelector(
  [selectMissionProgress, selectActiveMissionId],
  (missionProgress, activeMissionId) => activeMissionId ? missionProgress[activeMissionId] : null
);

export const selectMissionById = (id: string) => 
  createSelector(
    [selectMissions],
    (missions) => missions[id] || null
  );

export const selectMissionProgressById = (id: string) => 
  createSelector(
    [selectMissionProgress],
    (progress) => progress[id] || null
  );
