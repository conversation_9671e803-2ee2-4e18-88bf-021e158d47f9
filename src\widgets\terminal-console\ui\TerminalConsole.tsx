import React from 'react';
import { default as FeatureTerminalConsole } from '../../../features/terminal/ui/TerminalConsole';

interface TerminalConsoleProps {
  instanceId: string;
  className?: string;
}

const TerminalConsole: React.FC<TerminalConsoleProps> = ({ 
  instanceId,
  className 
}) => {
  return <FeatureTerminalConsole instanceId={instanceId} className={className} />;
};

export default TerminalConsole;